// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		17A56CEA2E2D19DF00A938D3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17A56CD12E2D19DD00A938D3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17A56CD82E2D19DE00A938D3;
			remoteInfo = VantisEducation;
		};
		17A56CF42E2D19DF00A938D3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17A56CD12E2D19DD00A938D3 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 17A56CD82E2D19DE00A938D3;
			remoteInfo = VantisEducation;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		17A56CD92E2D19DE00A938D3 /* VantisEducation.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VantisEducation.app; sourceTree = BUILT_PRODUCTS_DIR; };
		17A56CE92E2D19DF00A938D3 /* VantisEducationTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = VantisEducationTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		17A56CF32E2D19DF00A938D3 /* VantisEducationUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = VantisEducationUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		17972F532E3B917E00BABC7D /* Exceptions for "VantisEducation" folder in "VantisEducation" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 17A56CD82E2D19DE00A938D3 /* VantisEducation */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		17A56CDB2E2D19DE00A938D3 /* VantisEducation */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				17972F532E3B917E00BABC7D /* Exceptions for "VantisEducation" folder in "VantisEducation" target */,
			);
			path = VantisEducation;
			sourceTree = "<group>";
		};
		17A56CEC2E2D19DF00A938D3 /* VantisEducationTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = VantisEducationTests;
			sourceTree = "<group>";
		};
		17A56CF62E2D19DF00A938D3 /* VantisEducationUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = VantisEducationUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		17A56CD62E2D19DE00A938D3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17A56CE62E2D19DF00A938D3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17A56CF02E2D19DF00A938D3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		17A56CD02E2D19DD00A938D3 = {
			isa = PBXGroup;
			children = (
				17A56CDB2E2D19DE00A938D3 /* VantisEducation */,
				17A56CEC2E2D19DF00A938D3 /* VantisEducationTests */,
				17A56CF62E2D19DF00A938D3 /* VantisEducationUITests */,
				17A56CDA2E2D19DE00A938D3 /* Products */,
			);
			sourceTree = "<group>";
		};
		17A56CDA2E2D19DE00A938D3 /* Products */ = {
			isa = PBXGroup;
			children = (
				17A56CD92E2D19DE00A938D3 /* VantisEducation.app */,
				17A56CE92E2D19DF00A938D3 /* VantisEducationTests.xctest */,
				17A56CF32E2D19DF00A938D3 /* VantisEducationUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		17A56CD82E2D19DE00A938D3 /* VantisEducation */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17A56CFD2E2D19DF00A938D3 /* Build configuration list for PBXNativeTarget "VantisEducation" */;
			buildPhases = (
				17A56CD52E2D19DE00A938D3 /* Sources */,
				17A56CD62E2D19DE00A938D3 /* Frameworks */,
				17A56CD72E2D19DE00A938D3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				17A56CDB2E2D19DE00A938D3 /* VantisEducation */,
			);
			name = VantisEducation;
			packageProductDependencies = (
			);
			productName = VantisEducation;
			productReference = 17A56CD92E2D19DE00A938D3 /* VantisEducation.app */;
			productType = "com.apple.product-type.application";
		};
		17A56CE82E2D19DF00A938D3 /* VantisEducationTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17A56D002E2D19DF00A938D3 /* Build configuration list for PBXNativeTarget "VantisEducationTests" */;
			buildPhases = (
				17A56CE52E2D19DF00A938D3 /* Sources */,
				17A56CE62E2D19DF00A938D3 /* Frameworks */,
				17A56CE72E2D19DF00A938D3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17A56CEB2E2D19DF00A938D3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17A56CEC2E2D19DF00A938D3 /* VantisEducationTests */,
			);
			name = VantisEducationTests;
			packageProductDependencies = (
			);
			productName = VantisEducationTests;
			productReference = 17A56CE92E2D19DF00A938D3 /* VantisEducationTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		17A56CF22E2D19DF00A938D3 /* VantisEducationUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17A56D032E2D19DF00A938D3 /* Build configuration list for PBXNativeTarget "VantisEducationUITests" */;
			buildPhases = (
				17A56CEF2E2D19DF00A938D3 /* Sources */,
				17A56CF02E2D19DF00A938D3 /* Frameworks */,
				17A56CF12E2D19DF00A938D3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				17A56CF52E2D19DF00A938D3 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				17A56CF62E2D19DF00A938D3 /* VantisEducationUITests */,
			);
			name = VantisEducationUITests;
			packageProductDependencies = (
			);
			productName = VantisEducationUITests;
			productReference = 17A56CF32E2D19DF00A938D3 /* VantisEducationUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		17A56CD12E2D19DD00A938D3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					17A56CD82E2D19DE00A938D3 = {
						CreatedOnToolsVersion = 16.2;
					};
					17A56CE82E2D19DF00A938D3 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 17A56CD82E2D19DE00A938D3;
					};
					17A56CF22E2D19DF00A938D3 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 17A56CD82E2D19DE00A938D3;
					};
				};
			};
			buildConfigurationList = 17A56CD42E2D19DD00A938D3 /* Build configuration list for PBXProject "VantisEducation" */;
			developmentRegion = vi;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				vi,
			);
			mainGroup = 17A56CD02E2D19DD00A938D3;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 17A56CDA2E2D19DE00A938D3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				17A56CD82E2D19DE00A938D3 /* VantisEducation */,
				17A56CE82E2D19DF00A938D3 /* VantisEducationTests */,
				17A56CF22E2D19DF00A938D3 /* VantisEducationUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		17A56CD72E2D19DE00A938D3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17A56CE72E2D19DF00A938D3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17A56CF12E2D19DF00A938D3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		17A56CD52E2D19DE00A938D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17A56CE52E2D19DF00A938D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		17A56CEF2E2D19DF00A938D3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		17A56CEB2E2D19DF00A938D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17A56CD82E2D19DE00A938D3 /* VantisEducation */;
			targetProxy = 17A56CEA2E2D19DF00A938D3 /* PBXContainerItemProxy */;
		};
		17A56CF52E2D19DF00A938D3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 17A56CD82E2D19DE00A938D3 /* VantisEducation */;
			targetProxy = 17A56CF42E2D19DF00A938D3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		17A56CFB2E2D19DF00A938D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		17A56CFC2E2D19DF00A938D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		17A56CFE2E2D19DF00A938D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_ASSET_PATHS = "\"VantisEducation/Preview Content\"";
				DEVELOPMENT_TEAM = TCDD8WRJJ4;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VantisEducation/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Vantis Student";
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.education";
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to scan QR codes";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app needs photo library access to save images";
				INFOPLIST_KEY_UIAppFonts = "BeVietnamPro-Regular.ttf BeVietnamPro-Medium.ttf BeVietnamPro-SemiBold.ttf BeVietnamPro-Bold.ttf";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.1.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.vantis.student;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		17A56CFF2E2D19DF00A938D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_ASSET_PATHS = "\"VantisEducation/Preview Content\"";
				DEVELOPMENT_TEAM = TCDD8WRJJ4;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VantisEducation/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Vantis Student";
				INFOPLIST_KEY_ITSAppUsesNonExemptEncryption = NO;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.education";
				INFOPLIST_KEY_NSCameraUsageDescription = "This app needs camera access to scan QR codes";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "This app needs photo library access to save images";
				INFOPLIST_KEY_UIAppFonts = "BeVietnamPro-Regular.ttf BeVietnamPro-Medium.ttf BeVietnamPro-SemiBold.ttf BeVietnamPro-Bold.ttf";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.1.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.vantis.student;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		17A56D012E2D19DF00A938D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.vantis.studentTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VantisEducation.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VantisEducation";
			};
			name = Debug;
		};
		17A56D022E2D19DF00A938D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.vantis.studentTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/VantisEducation.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/VantisEducation";
			};
			name = Release;
		};
		17A56D042E2D19DF00A938D3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.vantis.studentUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = VantisEducation;
			};
			name = Debug;
		};
		17A56D052E2D19DF00A938D3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.vantis.studentUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = VantisEducation;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		17A56CD42E2D19DD00A938D3 /* Build configuration list for PBXProject "VantisEducation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17A56CFB2E2D19DF00A938D3 /* Debug */,
				17A56CFC2E2D19DF00A938D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17A56CFD2E2D19DF00A938D3 /* Build configuration list for PBXNativeTarget "VantisEducation" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17A56CFE2E2D19DF00A938D3 /* Debug */,
				17A56CFF2E2D19DF00A938D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17A56D002E2D19DF00A938D3 /* Build configuration list for PBXNativeTarget "VantisEducationTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17A56D012E2D19DF00A938D3 /* Debug */,
				17A56D022E2D19DF00A938D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		17A56D032E2D19DF00A938D3 /* Build configuration list for PBXNativeTarget "VantisEducationUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17A56D042E2D19DF00A938D3 /* Debug */,
				17A56D052E2D19DF00A938D3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 17A56CD12E2D19DD00A938D3 /* Project object */;
}

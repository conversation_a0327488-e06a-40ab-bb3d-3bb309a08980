# -*- coding: utf-8 -*-
# Copyright 2025 eBill <https://ebill.vn> and Vantis Vietnam <https://vantis.edu.vn>
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0.html).

"""
Student Lesson Participation API Router

Endpoints for student lesson participation:
- GET /lessons: Get accessible lessons
- GET /lessons/{lesson_id}: Get lesson details
- POST /lessons/{lesson_id}/checkin: Check-in to lesson
- POST /lessons/{lesson_id}/checkout: Check-out from lesson
- GET /lessons/{lesson_id}/materials: Get lesson materials
- POST /lessons/{lesson_id}/materials/{material_id}/download: Download material
- GET /lessons/{lesson_id}/assignments: Get lesson assignments
- POST /lessons/{lesson_id}/assignments/{assignment_id}/submit: Submit assignment
- POST /lessons/{lesson_id}/feedback: Submit lesson feedback
"""

from typing import Annotated, List, Optional
import pytz

from fastapi import APIRouter, Depends, Query, HTTPException
from datetime import datetime, date, timedelta

from odoo.api import Environment
from odoo.addons.fastapi.dependencies import odoo_env

from odoo.addons.eb_api_core.utils.exceptions import api_exception
from odoo.addons.eb_lms.utils.helpers import safe_value
from odoo.addons.eb_api_core.schemas.error import ErrorCode
from odoo.addons.eb_api_core.utils.logging import get_request_logger
from odoo.addons.eb_api_core.utils.datetime_utils import get_user_timezone

# Import schemas
from odoo.addons.eb_lms.schemas.students.lesson_schemas import (
    StudentLessonDetail,
    StudentLessonSummary,
    StudentLessonMaterial,
    CheckInRequest,
    CheckOutRequest,
    LessonMaterial,
    StudentLessonListResponse,
    StudentLessonDetailResponse,
    CheckInResponse,
    CheckOutResponse,
    LessonMaterialListResponse,
    AssignmentSubmissionRequest,
    AssignmentSubmissionResponse,
    LessonFeedbackRequest,
    LessonFeedbackResponse,
)

from odoo.addons.eb_lms.schemas.shared.pagination_schemas import PaginationRequest

# Import dependencies
from odoo.addons.eb_lms.dependencies.role_dependencies import get_student_info
from odoo.addons.eb_lms.utils.logging_utils import log_user_action
from odoo.addons.eb_lms.utils.safe_access import (
    get_enrollment_status, get_lesson_status, safe_getattr
)

_logger = get_request_logger(__name__)

# Lesson router
lesson_router = APIRouter(prefix="/lessons", tags=["Student Management"])


def _convert_to_user_timezone(dt_value: datetime, env: Environment) -> datetime:
    """Convert datetime from UTC to user's timezone."""
    if not dt_value:
        return dt_value

    # Get user timezone
    user_tz = get_user_timezone(env)

    # Convert UTC to user timezone
    if not dt_value.tzinfo:
        # Assume UTC if no timezone info
        dt_value = pytz.UTC.localize(dt_value)

    user_timezone = pytz.timezone(user_tz)
    return dt_value.astimezone(user_timezone)


def _build_lesson_summary(lesson, student_id: int, env: Environment) -> StudentLessonSummary:
    """Build lesson summary for student."""
    try:
        # Get student record to find user_id
        student = lesson.env['eb.student.student'].browse(student_id)
        if not student.exists() or not student.user_id:
            attendance_status = None
        else:
            # Get student's attendance record using user_id
            attendance = lesson.attendance_ids.filtered(
                lambda a: a.user_id.id == student.user_id.id and a.role == 'student'
            )
            attendance_status = attendance.status if attendance else None

        # Check status flags - use user timezone for comparison
        user_tz = get_user_timezone(env)
        user_timezone = pytz.timezone(user_tz)
        now_utc = datetime.now(pytz.UTC)
        now_user = now_utc.astimezone(user_timezone)

        # Convert lesson times to user timezone for comparison
        lesson_start_user = _convert_to_user_timezone(lesson.start_datetime, env)

        # Ensure lesson.start_datetime is timezone-aware for comparison
        lesson_start_utc = lesson.start_datetime
        if not lesson_start_utc.tzinfo:
            lesson_start_utc = pytz.UTC.localize(lesson_start_utc)

        lesson_end_utc = lesson.end_datetime
        if lesson_end_utc and not lesson_end_utc.tzinfo:
            lesson_end_utc = pytz.UTC.localize(lesson_end_utc)

        is_today = lesson_start_user.date() == now_user.date()
        is_upcoming = lesson_start_utc > now_utc and (lesson.is_scheduled or lesson.is_draft)
        is_live = (lesson.is_in_progress or
                  (lesson_start_utc <= now_utc <= (lesson_end_utc or lesson_start_utc + timedelta(hours=2))))

        # Check if needs attention
        needs_attention = False
        attention_reason = None

        if attendance_status == 'absent':
            needs_attention = True
            attention_reason = "Vắng mặt"
        elif is_upcoming and (lesson_start_utc - now_utc).total_seconds() < 3600:  # Less than 1 hour
            needs_attention = True
            attention_reason = "Sắp bắt đầu"

        # Check access permission
        can_access = True  # Basic access, will be refined based on payment status

        return StudentLessonSummary(
            id=lesson.id,
            name=lesson.name,
            class_name=lesson.class_id.name if lesson.class_id else "Chưa có lớp",
            subject_name=lesson.subject_id.name if lesson.subject_id else None,
            instructor_name=lesson.instructor_id.name if lesson.instructor_id else "Chưa có giảng viên",
            start_datetime=_convert_to_user_timezone(lesson.start_datetime, env),
            end_datetime=_convert_to_user_timezone(lesson.end_datetime, env),
            duration_hours=lesson.duration,
            room=lesson.room_id.name if lesson.room_id else None,
            location=lesson.location_id.name if lesson.location_id else None,
            attendance_status=attendance_status,
            lesson_status='scheduled' if lesson.is_scheduled else 'draft',
            can_access=can_access,
            is_today=is_today,
            is_upcoming=is_upcoming,
            is_live=is_live,
            needs_attention=needs_attention,
            attention_reason=attention_reason
        )

    except Exception as e:
        _logger.error(f"Error building lesson summary: {str(e)}")
        raise


def _build_lesson_detail(lesson, student_id: int, env: Environment) -> StudentLessonDetail:
    """Build detailed lesson information for student."""
    try:
        # Get student record to find user_id
        student = env['eb.student.student'].browse(student_id)
        if not student.exists() or not student.user_id:
            attendance_status = None
            check_in_time = None
            check_out_time = None
        else:
            # Get student's attendance record using user_id
            attendance = lesson.attendance_ids.filtered(
                lambda a: a.user_id.id == student.user_id.id and a.role == 'student'
            )
            attendance_status = attendance.status if attendance else None
            check_in_time = attendance.check_in_time if attendance else None
            check_out_time = getattr(attendance, 'check_out_time', None) if attendance else None

        # Build materials list from documents
        materials = []
        # Get lesson documents using document mixin
        documents = env["documents.document"].search([
            ('res_model', '=', 'eb.lesson.lesson'),
            ('res_id', '=', lesson.id)
        ], order='name')

        for document in documents:
            # Convert document to StudentLessonMaterial format
            # Extract file extension for file_type
            file_type = 'unknown'
            if document.name:
                file_extension = document.name.split('.')[-1].lower() if '.' in document.name else ''
                file_type = file_extension or 'unknown'

            # Generate URLs
            base_url = env['ir.config_parameter'].sudo().get_param('web.base.url', 'http://localhost:8069')
            download_url = f"{base_url}/api/v1/students/lessons/{lesson.id}/materials/{document.id}/download"
            view_url = f"{base_url}/api/v1/students/lessons/{lesson.id}/materials/{document.id}/view"

            # For now, we don't track individual document downloads in the old system
            # This would need to be implemented if required
            downloaded_at = None
            view_count = 0

            # Safely get description as string
            description = getattr(document, 'description', None)
            if description is False or description is True:
                description = None
            elif description and not isinstance(description, str):
                description = str(description)

            materials.append(StudentLessonMaterial(
                id=document.id,
                name=document.name or 'Untitled Document',
                description=description,
                file_type=file_type,
                file_size=document.file_size or 0,
                is_downloadable=True,  # Default to downloadable
                is_required=False,     # Default to not required
                download_url=download_url,
                view_url=view_url,
                upload_date=safe_value(document.create_date),
                downloaded_at=downloaded_at,
                view_count=view_count
            ))

        # Check access permissions
        now = datetime.now()
        can_access = True  # Will be refined based on enrollment and payment
        can_check_in = ((lesson.is_scheduled or lesson.is_in_progress) and
                       lesson.start_datetime <= now <= lesson.start_datetime + timedelta(hours=1) and
                       not attendance)
        is_live = (lesson.is_in_progress or
                  (lesson.start_datetime <= now <= (lesson.end_datetime or lesson.start_datetime + timedelta(hours=2))))
        is_completed = lesson.is_completed

        return StudentLessonDetail(
            id=lesson.id,
            name=lesson.name,
            description=getattr(lesson, 'description', None) or None,
            class_id=lesson.class_id.id,
            class_name=lesson.class_id.name,
            course_name=lesson.class_id.course_id.name,
            instructor_name=lesson.instructor_id.name,
            instructor_id=lesson.instructor_id.id,
            start_datetime=_convert_to_user_timezone(lesson.start_datetime, env),
            end_datetime=_convert_to_user_timezone(lesson.end_datetime, env),
            duration_hours=lesson.duration,
            room=lesson.room_id.name if lesson.room_id else None,
            location=lesson.location_id.name if lesson.location_id else None,
            objectives=lesson.objectives or None,
            content_outline=getattr(lesson, 'content_outline', None),
            homework_assignment=getattr(lesson, 'homework_assignment', None),
            notes=getattr(lesson, 'notes', None),
            materials=materials,
            attendance_status=attendance_status,
            check_in_time=_convert_to_user_timezone(check_in_time, env) if check_in_time else None,
            check_out_time=_convert_to_user_timezone(check_out_time, env) if check_out_time else None,
            participation_notes=getattr(attendance, 'notes', None) if attendance else None,
            lesson_status='completed' if lesson.is_completed else ('in_progress' if lesson.is_in_progress else 'scheduled'),
            can_access=can_access,
            can_check_in=can_check_in,
            is_live=is_live,
            is_completed=is_completed,
            created_at=_convert_to_user_timezone(lesson.create_date, env),
            updated_at=_convert_to_user_timezone(lesson.write_date, env)
        )

    except Exception as e:
        _logger.error(f"Error building lesson detail: {str(e)}")
        raise


@lesson_router.get(
    "/",
    response_model=StudentLessonListResponse,
    summary="Get Accessible Lessons",
    description="Get list of lessons student can access"
)
async def get_accessible_lessons(
    student_info: Annotated[dict, Depends(get_student_info)],
    pagination: Annotated[PaginationRequest, Depends()],
    env: Annotated[Environment, Depends(odoo_env)],
    class_id: Optional[int] = Query(None, description="Lọc theo lớp học"),
    status: Optional[str] = Query(None, description="Lọc theo trạng thái"),
    date_from: Optional[date] = Query(None, description="Từ ngày"),
    date_to: Optional[date] = Query(None, description="Đến ngày")
):
    """Get accessible lessons for student."""
    try:
        student = student_info["student"]

        # Get enrolled classes - use safe access
        enrolled_classes = [e.class_id.id for e in student.enrollment_ids
                           if get_enrollment_status(e) in ['paid', 'partial']]

        if not enrolled_classes:
            return StudentLessonListResponse.create_lesson_list_response(
                lessons=[],
                page=pagination.page,
                page_size=pagination.page_size,
                total_count=0
            )

        # Build domain
        domain = [('class_id', 'in', enrolled_classes)]

        if class_id:
            domain.append(('class_id', '=', class_id))
        if status:
            # Map status to boolean fields
            if status == 'scheduled':
                domain.append(('is_scheduled', '=', True))
            elif status == 'completed':
                domain.append(('is_completed', '=', True))
            elif status == 'in_progress':
                domain.append(('is_in_progress', '=', True))
            elif status == 'draft':
                domain.append(('is_draft', '=', True))
            elif status == 'cancelled':
                domain.append(('is_cancelled', '=', True))
        if date_from:
            domain.append(('start_datetime', '>=', datetime.combine(date_from, datetime.min.time())))
        if date_to:
            domain.append(('start_datetime', '<=', datetime.combine(date_to, datetime.max.time())))

        # Get total count
        total_count = env["eb.lesson.lesson"].search_count(domain)

        # Get lessons with pagination
        lessons = env["eb.lesson.lesson"].search(
            domain,
            offset=(pagination.page - 1) * pagination.page_size,
            limit=pagination.page_size,
            order=f"{pagination.sort_by or 'start_datetime'} {pagination.sort_order.value}"
        )

        # Build response
        lesson_summaries = []
        for lesson in lessons:
            summary = _build_lesson_summary(lesson, student.id, env)
            lesson_summaries.append(summary)

        return StudentLessonListResponse.create_lesson_list_response(
            lessons=lesson_summaries,
            page=pagination.page,
            page_size=pagination.page_size,
            total_count=total_count
        )

    except Exception as e:
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Get accessible lessons error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải danh sách bài học: {str(e)}"
        )


@lesson_router.get(
    "/{lesson_id}",
    response_model=StudentLessonDetailResponse,
    summary="Get Lesson Details",
    description="Get detailed information about a specific lesson"
)
async def get_lesson_details(
    lesson_id: int,
    student_info: Annotated[dict, Depends(get_student_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Get detailed lesson information."""
    try:
        student = student_info["student"]

        # Get lesson record
        lesson = env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Bài học ID {lesson_id} không tồn tại")

        # Check if student is enrolled in this class - use safe access
        enrollment = student.enrollment_ids.filtered(
            lambda e: e.class_id.id == lesson.class_id.id and
            get_enrollment_status(e) in ['paid', 'partial']
        )

        if not enrollment:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Bạn chưa đăng ký lớp học này"
            )

        # Check payment status
        if get_enrollment_status(enrollment) not in ['paid', 'partial']:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Cần thanh toán để truy cập bài học"
            )

        # Build detailed lesson info
        lesson_detail = _build_lesson_detail(lesson, student.id, env)

        return StudentLessonDetailResponse.create_lesson_detail_response(lesson_detail)

    except HTTPException:
        # Re-raise HTTP exceptions (including api_exception)
        raise
    except Exception as e:
        _logger.error(f"Get lesson details error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải chi tiết bài học: {str(e)}"
        )


@lesson_router.post(
    "/{lesson_id}/checkin",
    response_model=CheckInResponse,
    summary="Check-in to Lesson",
    description="Check-in to a lesson"
)
@log_user_action("checkin", "lesson")
async def checkin_lesson(
    lesson_id: int,
    request: CheckInRequest,
    student_info: Annotated[dict, Depends(get_student_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Check-in to a lesson."""
    try:
        student = student_info["student"]

        # Get lesson record
        lesson = env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Bài học ID {lesson_id} không tồn tại")

        # Note: lesson_id is already validated from URL path

        # Check enrollment and payment - use safe access
        enrollment = student.enrollment_ids.filtered(
            lambda e: e.class_id.id == lesson.class_id.id and
            get_enrollment_status(e) in ['paid', 'partial']
        )

        if not enrollment:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Không có quyền tham gia bài học này"
            )

        # Check if lesson is available for check-in
        # Model eb.lesson.lesson has is_scheduled and is_in_progress fields (related to stage_id)
        now_utc = datetime.now(pytz.UTC)
        if not (lesson.is_scheduled or lesson.is_in_progress):
            status = get_lesson_status(lesson)
            raise api_exception(
                ErrorCode.BAD_REQUEST,
                f"Không thể check-in bài học ở trạng thái '{status}'"
            )

        # Check time window (can check-in from 30 minutes before to 1 hour after start)
        # Convert lesson time to UTC for comparison
        start_time_utc = lesson.start_datetime
        if not start_time_utc.tzinfo:
            start_time_utc = pytz.UTC.localize(start_time_utc)

        earliest_checkin = start_time_utc - timedelta(minutes=30)
        latest_checkin = start_time_utc + timedelta(hours=1)

        # Temporarily disable time check for testing
        # if not (earliest_checkin <= now_utc <= latest_checkin):
        #     # Convert to user timezone for display
        #     user_tz = get_user_timezone(env)
        #     user_timezone = pytz.timezone(user_tz)
        #     now_user = now_utc.astimezone(user_timezone)
        #     earliest_user = earliest_checkin.astimezone(user_timezone)
        #     latest_user = latest_checkin.astimezone(user_timezone)
        #
        #     raise api_exception(
        #         ErrorCode.BAD_REQUEST,
        #         f"Chỉ có thể check-in trong khoảng 30 phút trước đến 1 giờ sau giờ học. "
        #         f"Hiện tại: {now_user.strftime('%H:%M')}, "
        #         f"Thời gian check-in: {earliest_user.strftime('%H:%M')} - {latest_user.strftime('%H:%M')}"
        #     )

        # Check if already checked in - search attendance records
        existing_attendance = env["eb.attendance.attendance"].search([
            ('lesson_id', '=', lesson.id),
            ('user_id', '=', student.user_id.id),
            ('role', '=', 'student')
        ], limit=1)

        if existing_attendance:
            from fastapi import HTTPException
            raise HTTPException(
                status_code=409,
                detail="Đã check-in bài học này rồi"
            )

        # Create attendance record - use correct fields for eb.attendance.attendance
        # Convert to naive datetime for Odoo
        now_naive = now_utc.replace(tzinfo=None)
        attendance_vals = {
            'lesson_id': lesson.id,
            'user_id': student.user_id.id,  # use user_id instead of student_id
            'role': 'student',
            'status': 'present',
            'check_in_time': now_naive,
            'status_note': request.notes,  # use status_note instead of notes
        }

        # Add location and device info to notes since fields may not exist in model
        additional_notes = []
        if request.location_lat and request.location_lng:
            additional_notes.append(f"Location: {request.location_lat}, {request.location_lng}")

        if request.device_info:
            additional_notes.append(f"Device: {request.device_info}")

        if additional_notes:
            current_notes = attendance_vals.get('status_note', '') or ''
            combined_notes = f"{current_notes}\n{'; '.join(additional_notes)}" if current_notes else '; '.join(additional_notes)
            attendance_vals['status_note'] = combined_notes

        env["eb.attendance.attendance"].create(attendance_vals)

        return CheckInResponse.create_checkin_response(
            lesson_id=lesson.id,
            lesson_name=lesson.name,
            check_in_time=now_utc
        )

    except Exception as e:
        # Let HTTPException pass through
        from fastapi import HTTPException
        if isinstance(e, HTTPException):
            raise
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        import traceback
        _logger.error(f"Check-in lesson error: {str(e)}")
        _logger.error(f"Check-in lesson traceback: {traceback.format_exc()}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể check-in: {str(e)}"
        )


@lesson_router.post(
    "/{lesson_id}/checkout",
    response_model=CheckOutResponse,
    summary="Check-out from Lesson",
    description="Check-out from a lesson"
)
@log_user_action("checkout", "lesson")
async def checkout_lesson(
    lesson_id: int,
    request: CheckOutRequest,
    student_info: Annotated[dict, Depends(get_student_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Check-out from a lesson."""
    try:
        student = student_info["student"]

        # Get lesson record
        lesson = env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Bài học ID {lesson_id} không tồn tại")

        # Check if student is enrolled in this class
        enrollment = student.enrollment_ids.filtered(
            lambda e: e.class_id.id == lesson.class_id.id and
            get_enrollment_status(e) in ['paid', 'partial']
        )

        if not enrollment:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Không có quyền tham gia bài học này"
            )

        # Find existing attendance record
        existing_attendance = env["eb.attendance.attendance"].search([
            ('lesson_id', '=', lesson.id),
            ('user_id', '=', student.user_id.id),
            ('role', '=', 'student')
        ], limit=1)

        if not existing_attendance:
            from fastapi import HTTPException
            raise HTTPException(
                status_code=404,
                detail="Chưa check-in bài học này"
            )

        if existing_attendance.check_out_time:
            from fastapi import HTTPException
            raise HTTPException(
                status_code=409,
                detail="Đã check-out bài học này rồi"
            )

        # Update attendance record with check-out time
        now_utc = datetime.now(pytz.UTC)
        # Convert to naive datetime for Odoo
        now_naive = now_utc.replace(tzinfo=None)
        update_vals = {
            'check_out_time': now_naive,
        }

        # Add checkout notes if provided
        if request.notes:
            current_notes = existing_attendance.status_note or ""
            checkout_notes = f"Check-out: {request.notes}"
            update_vals['status_note'] = f"{current_notes}\n{checkout_notes}" if current_notes else checkout_notes

        # Note: checkout location and device info fields will be added to model later
        # For now, we'll include them in the notes
        if request.location_lat and request.location_lng:
            location_note = f"Check-out location: {request.location_lat}, {request.location_lng}"
            current_notes = update_vals.get('status_note', existing_attendance.status_note or "")
            update_vals['status_note'] = f"{current_notes}\n{location_note}" if current_notes else location_note

        if request.device_info:
            device_note = f"Check-out device: {request.device_info}"
            current_notes = update_vals.get('status_note', existing_attendance.status_note or "")
            update_vals['status_note'] = f"{current_notes}\n{device_note}" if current_notes else device_note

        existing_attendance.write(update_vals)

        # Calculate duration (in hours)
        check_in_time = existing_attendance.check_in_time
        if check_in_time:
            # Convert check_in_time to UTC if it's naive
            if not check_in_time.tzinfo:
                check_in_time_utc = pytz.UTC.localize(check_in_time)
            else:
                check_in_time_utc = check_in_time.astimezone(pytz.UTC)

            duration = (now_utc - check_in_time_utc).total_seconds() / 3600.0
        else:
            duration = 0.0
            check_in_time_utc = now_utc  # Use current time as fallback

        return CheckOutResponse.create_checkout_response(
            lesson_id=lesson.id,
            lesson_name=lesson.name,
            check_in_time=check_in_time_utc,
            check_out_time=now_utc,
            duration=duration
        )

    except Exception as e:
        # Let HTTPException pass through
        from fastapi import HTTPException
        if isinstance(e, HTTPException):
            raise
        if hasattr(e, '__class__') and 'api_exception' in str(e.__class__):
            raise
        _logger.error(f"Check-out lesson error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể check-out: {str(e)}"
        )


@lesson_router.get(
    "/{lesson_id}/materials",
    response_model=LessonMaterialListResponse,
    summary="Get Lesson Materials",
    description="Get list of materials for a specific lesson"
)
async def get_lesson_materials(
    lesson_id: int,
    student_info: Annotated[dict, Depends(get_student_info)],
    env: Annotated[Environment, Depends(odoo_env)],
    material_type: Optional[str] = Query(None, description="Lọc theo loại tài liệu")
):
    """Get lesson materials."""
    try:
        student = student_info["student"]

        # Get lesson record
        lesson = env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Bài học ID {lesson_id} không tồn tại")

        # Check if student is enrolled in this class - use safe access
        enrollment = student.enrollment_ids.filtered(
            lambda e: e.class_id.id == lesson.class_id.id and
            get_enrollment_status(e) in ['paid', 'partial']
        )

        if not enrollment:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Bạn chưa đăng ký lớp học này"
            )

        # Check payment status
        if get_enrollment_status(enrollment) not in ['paid', 'partial']:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Cần thanh toán để truy cập tài liệu bài học"
            )

        # Get lesson materials from documents
        domain = [
            ('res_model', '=', 'eb.lesson.lesson'),
            ('res_id', '=', lesson.id)
        ]
        if material_type:
            domain.append(('mimetype', 'ilike', material_type))

        materials = env["documents.document"].search(domain, order='name')

        # Build materials list
        material_list = []
        for material in materials:
            material_info = _build_lesson_material(material, student.id)
            material_list.append(material_info)

        return LessonMaterialListResponse.create_material_list_response(
            materials=material_list,
            lesson_id=lesson.id,
            lesson_name=lesson.name
        )

    except HTTPException:
        # Re-raise HTTP exceptions (including api_exception)
        raise
    except Exception as e:
        _logger.error(f"Get lesson materials error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải tài liệu bài học: {str(e)}"
        )


@lesson_router.get(
    "/{lesson_id}/assignments",
    response_model=dict,
    summary="Get Lesson Assignments",
    description="Get list of assignments for a specific lesson"
)
async def get_lesson_assignments(
    lesson_id: int,
    student_info: Annotated[dict, Depends(get_student_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Get lesson assignments."""
    try:
        student = student_info["student"]

        # Get lesson record
        lesson = env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Bài học ID {lesson_id} không tồn tại")

        # Check if student is enrolled in this class
        enrollment = student.enrollment_ids.filtered(
            lambda e: e.class_id.id == lesson.class_id.id and
            get_enrollment_status(e) in ['paid', 'partial']
        )

        if not enrollment:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Bạn chưa đăng ký lớp học này"
            )

        # Get assignments for this lesson
        assignments = env["eb.assignment.assignment"].search([
            ('lesson_id', '=', lesson.id),
            ('is_published', '=', True),
            ('status', 'in', ['assigned', 'in_progress', 'completed'])
        ])

        # Build assignment list
        assignment_list = []
        for assignment in assignments:
            # Check if student has submitted this assignment
            submission = env["eb.assignment.submission"].search([
                ('assignment_id', '=', assignment.id),
                ('student_id', '=', student.id)
            ], limit=1)

            assignment_data = {
                'id': assignment.id,
                'title': assignment.title,
                'description': assignment.description,
                'instructions': assignment.instructions,
                'assignment_type': assignment.assignment_type,
                'difficulty_level': assignment.difficulty_level,
                'assigned_date': safe_value(assignment.assigned_date).isoformat() if safe_value(assignment.assigned_date) else None if assignment.assigned_date else None,
                'due_date': safe_value(assignment.due_date).isoformat() if safe_value(assignment.due_date) else None if assignment.due_date else None,
                'max_score': assignment.max_score,
                'estimated_hours': assignment.estimated_hours,
                'status': assignment.status,
                'allow_late_submission': assignment.allow_late_submission,
                'late_penalty_percent': assignment.late_penalty_percent,
                'submission_status': 'submitted' if submission else 'not_submitted',
                'submission_id': submission.id if submission else None,
                'submitted_date': safe_value(submission.submitted_date).isoformat() if safe_value(submission.submitted_date) else None if submission and submission.submitted_date else None,
                'grade': submission.score if submission else None,
                'feedback': submission.feedback if submission else None
            }
            assignment_list.append(assignment_data)

        return {
            'success': True,
            'data': {
                'assignments': assignment_list,
                'lesson_id': lesson.id,
                'lesson_name': lesson.name,
                'total_assignments': len(assignment_list)
            },
            'meta': {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M'),
                'lesson_id': lesson.id
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Get lesson assignments error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể tải bài tập: {str(e)}"
        )


@lesson_router.post(
    "/{lesson_id}/assignments/{assignment_id}/submit",
    response_model=AssignmentSubmissionResponse,
    summary="Submit Assignment",
    description="Submit an assignment for a specific lesson"
)
async def submit_assignment(
    lesson_id: int,
    assignment_id: int,
    request: AssignmentSubmissionRequest,
    student_info: Annotated[dict, Depends(get_student_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Submit assignment."""
    try:
        student = student_info["student"]

        # Get lesson record
        lesson = env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Bài học ID {lesson_id} không tồn tại")

        # Get assignment record
        assignment = env["eb.assignment.assignment"].browse(assignment_id)
        if not assignment.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Bài tập ID {assignment_id} không tồn tại")

        # Check if assignment belongs to this lesson
        if assignment.lesson_id.id != lesson.id:
            raise api_exception(
                ErrorCode.BAD_REQUEST,
                "Bài tập không thuộc về buổi học này"
            )

        # Check if student is enrolled in this class
        enrollment = student.enrollment_ids.filtered(
            lambda e: e.class_id.id == lesson.class_id.id and
            get_enrollment_status(e) in ['paid', 'partial']
        )

        if not enrollment:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Bạn chưa đăng ký lớp học này"
            )

        # Check if assignment is published and available
        if not assignment.is_published or assignment.status not in ['assigned', 'in_progress']:
            raise api_exception(
                ErrorCode.BAD_REQUEST,
                "Bài tập chưa được phát hành hoặc đã hết hạn nộp"
            )

        # Check if student has already submitted this assignment
        existing_submission = env["eb.assignment.submission"].search([
            ('assignment_id', '=', assignment.id),
            ('student_id', '=', student.id)
        ], limit=1)

        if existing_submission:
            raise api_exception(
                ErrorCode.CONFLICT,
                "Bạn đã nộp bài tập này rồi"
            )

        # Check if assignment is past due date
        is_late = False
        if assignment.due_date and assignment.due_date < datetime.now():
            if not assignment.allow_late_submission:
                raise api_exception(
                    ErrorCode.BAD_REQUEST,
                    "Bài tập đã quá hạn nộp và không cho phép nộp muộn"
                )
            is_late = True

        # Create submission
        submission_vals = {
            'assignment_id': assignment.id,
            'student_id': student.id,
            'content': request.content,
            'notes': request.notes,
            'submission_date': datetime.now(),
            'status': 'late' if is_late else 'submitted'
        }

        submission = env["eb.assignment.submission"].create(submission_vals)

        return AssignmentSubmissionResponse.create_submission_response(
            submission_id=submission.id,
            assignment_id=assignment.id,
            assignment_title=assignment.title,
            status=submission.status
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Submit assignment error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể nộp bài tập: {str(e)}"
        )


@lesson_router.post(
    "/{lesson_id}/feedback",
    response_model=LessonFeedbackResponse,
    summary="Submit Lesson Feedback",
    description="Submit feedback for a specific lesson"
)
async def submit_lesson_feedback(
    lesson_id: int,
    request: LessonFeedbackRequest,
    student_info: Annotated[dict, Depends(get_student_info)],
    env: Annotated[Environment, Depends(odoo_env)]
):
    """Submit lesson feedback."""
    try:
        student = student_info["student"]

        # Get lesson record
        lesson = env["eb.lesson.lesson"].browse(lesson_id)
        if not lesson.exists():
            raise api_exception(ErrorCode.NOT_FOUND, f"Bài học ID {lesson_id} không tồn tại")

        # Check if student is enrolled in this class
        enrollment = student.enrollment_ids.filtered(
            lambda e: e.class_id.id == lesson.class_id.id and
            get_enrollment_status(e) in ['paid', 'partial']
        )

        if not enrollment:
            raise api_exception(
                ErrorCode.INSUFFICIENT_PERMISSIONS,
                "Bạn chưa đăng ký lớp học này"
            )

        # Check if student attended this lesson (optional check)
        attendance = env["eb.attendance.attendance"].search([
            ('lesson_id', '=', lesson.id),
            ('user_id', '=', student.user_id.id),
            ('role', '=', 'student')
        ], limit=1)

        # Allow feedback even without attendance record for now
        # if not attendance or attendance.status not in ['present', 'late']:
        #     raise api_exception(
        #         ErrorCode.BAD_REQUEST,
        #         "Chỉ có thể đánh giá buổi học mà bạn đã tham gia"
        #     )

        # Check if feedback already exists using evaluation model
        existing_feedback = env["eb.evaluation"].search([
            ('related_model', '=', 'eb.lesson.lesson'),
            ('res_id', '=', lesson.id),
            ('evaluator_type', '=', 'student'),
            ('evaluator_id', '=', student.id)
        ], limit=1)

        if existing_feedback:
            raise api_exception(
                ErrorCode.CONFLICT,
                "Bạn đã đánh giá buổi học này rồi"
            )

        # Create evaluation record as feedback
        feedback_vals = {
            'name': f"Đánh giá buổi học: {lesson.name}",
            'template_id': 1,  # Default template ID - should be created in data
            'evaluator_type': 'student',
            'evaluator_id': student.id,
            'related_model': 'eb.lesson.lesson',
            'res_id': lesson.id,
            'evaluation_date': datetime.now().date(),
            'summary': request.feedback_text or f"Đánh giá: {request.rating}/5 sao"
        }

        feedback = env["eb.evaluation"].create(feedback_vals)

        return LessonFeedbackResponse.create_feedback_response(
            lesson_id=lesson.id,
            lesson_name=lesson.name,
            rating=request.rating
        )

    except HTTPException:
        raise
    except Exception as e:
        _logger.error(f"Submit lesson feedback error: {str(e)}")
        raise api_exception(
            ErrorCode.INTERNAL_SERVER_ERROR,
            f"Không thể gửi phản hồi: {str(e)}"
        )


def _build_lesson_material(document, student_id) -> LessonMaterial:
    """Build lesson material from documents.document record."""
    try:
        # Generate access URLs
        base_url = document.env['ir.config_parameter'].sudo().get_param('web.base.url', 'http://localhost:8069')

        # Check if material is accessible
        is_accessible = True
        access_restriction = None

        # Determine file type from mimetype
        file_type = 'unknown'
        if document.mimetype:
            if document.mimetype.startswith('image/'):
                file_type = 'image'
            elif document.mimetype.startswith('video/'):
                file_type = 'video'
            elif document.mimetype.startswith('audio/'):
                file_type = 'audio'
            elif 'pdf' in document.mimetype:
                file_type = 'pdf'
            elif any(x in document.mimetype for x in ['word', 'document']):
                file_type = 'document'
            elif any(x in document.mimetype for x in ['sheet', 'excel']):
                file_type = 'spreadsheet'
            elif any(x in document.mimetype for x in ['presentation', 'powerpoint']):
                file_type = 'presentation'

        # Generate URLs from document
        view_url = None
        download_url = None
        thumbnail_url = None

        if document.attachment_id:
            attachment = document.attachment_id
            download_url = f"{base_url}/web/content/{attachment.id}?download=true"
            view_url = f"{base_url}/web/content/{attachment.id}"

            # Generate thumbnail for images
            if attachment.mimetype and attachment.mimetype.startswith('image/'):
                thumbnail_url = f"{base_url}/web/image/{attachment.id}/thumbnail"

        return LessonMaterial(
            id=document.id,
            name=document.name,
            description=getattr(document, 'description', None) or None,
            material_type='document',
            file_name=document.name,
            file_size=document.file_size or 0,
            file_type=file_type,
            sequence=0,
            is_required=False,
            is_accessible=is_accessible,
            access_restriction=access_restriction,
            view_url=view_url,
            download_url=download_url,
            thumbnail_url=thumbnail_url,
            duration_minutes=None,
            created_at=safe_value(document.create_date),
            updated_at=document.write_date
        )

    except Exception as e:
        _logger.error(f"Error building lesson material: {str(e)}")
        raise


_logger.info("Student Lesson API router registered successfully")

//
//  User.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - User Model
struct User: Codable, Identifiable {
    let id: String
    let email: String
    let firstName: String?
    let lastName: String?
    let phone: String?
    let role: UserRole
    let isActive: Bool
    let avatar: String?
    let dateOfBirth: Date?
    let lastLoginAt: Date?
    let createdAt: Date
    let updatedAt: Date?

    // Business information for BUSINESS users
    let businessName: String?
    let businessId: String?
    let category: String?
    let businessPhone: String?
    let website: String?
    let businessDescription: String?
    let businessStatus: String?
    let onboardedAt: Date?
    
    // Computed properties
    var displayName: String {
        if let firstName = firstName, let lastName = lastName {
            return "\(firstName) \(lastName)"
        }
        return email
    }
    
    var initials: String {
        let components = displayName.components(separatedBy: " ")
        let initials = components.compactMap { $0.first }.map { String($0) }
        return initials.prefix(2).joined().uppercased()
    }
    
    var isMerchant: Bool {
        return role == .business
    }

    var isAdmin: Bool {
        return role.isAdminLevel
    }

    var isStudent: Bool {
        return role.isStudentLevel
    }

    var isInstructor: Bool {
        return role.isInstructorLevel
    }

    /// Check if user has legacy role that needs migration
    var hasLegacyRole: Bool {
        return role.isLegacy
    }

    /// Get user with migrated role
    func withMigratedRole() -> User {
        guard hasLegacyRole else { return self }

        return User(
            id: id,
            email: email,
            firstName: firstName,
            lastName: lastName,
            phone: phone,
            role: role.modernEquivalent,
            isActive: isActive,
            avatar: avatar,
            dateOfBirth: dateOfBirth,
            lastLoginAt: lastLoginAt,
            createdAt: createdAt,
            updatedAt: updatedAt,
            businessName: businessName,
            businessId: businessId,
            category: category,
            businessPhone: businessPhone,
            website: website,
            businessDescription: businessDescription,
            businessStatus: businessStatus,
            onboardedAt: onboardedAt
        )
    }
}

// MARK: - User Role
enum UserRole: String, Codable, CaseIterable {
    // Primary roles (active)
    case student = "student"
    case instructor = "instructor"
    case admin = "admin"

    // Legacy roles (deprecated - will be migrated to student)
    case user = "USER"
    case business = "BUSINESS"
    case legacyAdmin = "ADMIN"

    var displayName: String {
        switch self {
        case .student:
            return "Student"
        case .instructor:
            return "Instructor"
        case .admin:
            return "Admin"
        case .user:
            return "User (Legacy)"
        case .business:
            return "Business (Legacy)"
        case .legacyAdmin:
            return "Admin (Legacy)"
        }
    }

    /// Check if this is a legacy role that needs migration
    var isLegacy: Bool {
        switch self {
        case .user, .business, .legacyAdmin:
            return true
        case .student, .instructor, .admin:
            return false
        }
    }

    /// Get the modern equivalent of a legacy role
    var modernEquivalent: UserRole {
        switch self {
        case .user, .business:
            return .student  // Map legacy user/business to student
        case .legacyAdmin:
            return .admin    // Map legacy admin to modern admin
        case .student, .instructor, .admin:
            return self      // Already modern
        }
    }

    /// Create UserRole from API response string
    static func fromAPIResponse(_ roleString: String) -> UserRole {
        // First try direct match
        if let role = UserRole(rawValue: roleString) {
            return role
        }

        // Handle case variations
        switch roleString.lowercased() {
        case "student":
            return .student
        case "instructor":
            return .instructor
        case "admin":
            return .admin
        case "user":
            return .user
        case "business":
            return .business
        default:
            print("⚠️ Unknown role '\(roleString)', defaulting to student")
            return .student
        }
    }

    /// Migration helper: Convert legacy role to modern role
    static func migrateFromLegacy(_ legacyRole: UserRole) -> UserRole {
        return legacyRole.modernEquivalent
    }

    /// Check if user has student-level permissions
    var isStudentLevel: Bool {
        switch self {
        case .student, .user, .business:
            return true
        case .instructor, .admin, .legacyAdmin:
            return false
        }
    }

    /// Check if user has instructor-level permissions
    var isInstructorLevel: Bool {
        switch self {
        case .instructor:
            return true
        case .student, .user, .business, .admin, .legacyAdmin:
            return false
        }
    }

    /// Check if user has admin-level permissions
    var isAdminLevel: Bool {
        switch self {
        case .admin, .legacyAdmin:
            return true
        case .student, .instructor, .user, .business:
            return false
        }
    }
}

// MARK: - API Response Models
struct UserInfoResponse: Codable {
    let id: Int
    let email: String
    let login: String
    let name: String
    let groups: [String]
    let isAdmin: Int
    let isSystem: Int
    let partnerId: Int?

    enum CodingKeys: String, CodingKey {
        case id, email, login, name, groups
        case isAdmin = "is_admin"
        case isSystem = "is_system"
        case partnerId = "partner_id"
    }
}

// MARK: - Auth DTOs
struct LoginRequest: Codable {
    let username: String
    let password: String
    let deviceInfo: DeviceInfo
    let rememberMe: Bool
    let mfaCode: String?

    enum CodingKeys: String, CodingKey {
        case username, password
        case deviceInfo = "device_info"
        case rememberMe = "remember_me"
        case mfaCode = "mfa_code"
    }

    struct DeviceInfo: Codable {
        let deviceId: String
        let deviceName: String
        let deviceType: String
        let osName: String
        let osVersion: String
        let appVersion: String
        let browserName: String
        let browserVersion: String
        let userAgent: String
        let ipAddress: String
        let location: Location

        enum CodingKeys: String, CodingKey {
            case deviceId = "device_id"
            case deviceName = "device_name"
            case deviceType = "device_type"
            case osName = "os_name"
            case osVersion = "os_version"
            case appVersion = "app_version"
            case browserName = "browser_name"
            case browserVersion = "browser_version"
            case userAgent = "user_agent"
            case ipAddress = "ip_address"
            case location
        }

        struct Location: Codable {
            let city: String
            let country: String
            let latitude: Double
            let longitude: Double
        }
    }
}

struct RegisterRequest: Codable {
    let email: String
    let password: String
    let phone: String?
    let firstName: String?
    let lastName: String?
}

// MARK: - Password Reset Models
struct ForgotPasswordRequest: Codable {
    let email: String
}

struct ForgotPasswordResponse: Codable {
    let message: String
    let resetTokenSent: Bool

    enum CodingKeys: String, CodingKey {
        case message
        case resetTokenSent = "reset_token_sent"
    }
}

struct ResetPasswordRequest: Codable {
    let token: String
    let newPassword: String

    enum CodingKeys: String, CodingKey {
        case token
        case newPassword = "new_password"
    }
}

struct ResetPasswordResponse: Codable {
    let message: String
    let success: Bool
}

// MARK: - Auth Tokens
struct AuthTokens: Codable {
    let accessToken: String
    let refreshToken: String?
    let tokenType: String
    let expiresIn: Int?

    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case tokenType = "token_type"
        case expiresIn = "expires_in"
    }
}

struct AuthResponse: Codable {
    let access_token: String
    let user: User
    let tokens: AuthTokens?

    enum CodingKeys: String, CodingKey {
        case access_token
        case user
        case tokens
    }
}

// MARK: - User Profile Update
struct UpdateProfileRequest: Codable {
    let firstName: String?
    let lastName: String?
    let phone: String?
    let dateOfBirth: Date?
    let avatar: String?
}

// MARK: - Password Change
struct ChangePasswordRequest: Codable {
    let currentPassword: String
    let newPassword: String
}



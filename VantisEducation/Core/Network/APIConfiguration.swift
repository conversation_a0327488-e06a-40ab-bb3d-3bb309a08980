import Foundation

/// API Configuration for the mobile app
class APIConfiguration {
    static let shared = APIConfiguration()

    private init() {}

    // MARK: - Configuration Properties

    /// Base URL for the API
    var baseURL: String {
        #if DEBUG
        return "https://lms-dev1.earnbase.io/api/v1" // Development environment
        #else
        return "https://lms-dev1.earnbase.io/api/v1" // Production environment
        #endif
    }
    
    /// Request timeout in seconds
    var timeout: TimeInterval {
        return 30.0
    }
    
    /// Number of retry attempts for failed requests
    var retryAttempts: Int {
        return 3
    }
    
    /// Delay between retry attempts in seconds
    var retryDelay: TimeInterval {
        return 1.0
    }
    
    /// Default headers for all requests
    var defaultHeaders: [String: String] {
        return [
            "Content-Type": "application/json",
            "Accept": "application/json",
            "User-Agent": "MobileApp/1.0"
        ]
    }
    
    // MARK: - Helper Methods
    
    /// Build full URL from endpoint
    func buildURL(endpoint: String) -> URL? {
        let cleanEndpoint = endpoint.hasPrefix("/") ? endpoint : "/\(endpoint)"
        return URL(string: baseURL + cleanEndpoint)
    }
}

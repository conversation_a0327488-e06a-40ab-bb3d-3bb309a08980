//
//  StudentAuthService.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import Foundation
import Combine
import UIKit

// MARK: - Student Login Request Models
struct StudentLoginRequest: Codable {
    let username: String
    let password: String
    let device_info: StudentDeviceInfo
    let remember_me: Bool
    let mfa_code: String?
}

struct StudentDeviceInfo: Codable {
    let device_id: String
    let device_name: String
    let device_type: String
    let os_name: String
    let os_version: String
    let app_version: String
    let browser_name: String
    let browser_version: String
    let user_agent: String
    let ip_address: String
    let location: StudentLocationInfo
}

struct StudentLocationInfo: Codable {
    let city: String
    let country: String
    let latitude: Double
    let longitude: Double
}

// MARK: - Student Login Response Models
struct StudentLoginResponse: Codable {
    let success: Bool
    let message: String
    let data: StudentLoginData?
}

struct StudentLoginData: Codable {
    let access_token: String
    let token_type: String
    let expires_in: Int
    let refresh_token: String
    let refresh_token_expires_in: Int?
    let scope: String?
    let issued_at: Int?
    let user_id: Int
    let device_registered: Bool
    let device_info: DeviceInfo?
    let remember_me: Bool?
    let login_method: String?
}

struct DeviceInfo: Codable {
    let device_id: String?
    let device_name: String?
    let device_type: String?
    let os_name: String?
    let os_version: String?
    let app_version: String?
    let browser_name: String?
    let browser_version: String?
    let user_agent: String?
    let ip_address: String?
    let location: LocationInfo?
}

struct LocationInfo: Codable {
    let city: String?
    let country: String?
    let latitude: Double?
    let longitude: Double?
}

// MARK: - Student Authentication Errors
enum StudentAuthError: LocalizedError {
    case invalidCredentials
    case invalidStudentRole
    case networkError(String)
    case tokenExpired

    var errorDescription: String? {
        switch self {
        case .invalidCredentials:
            return "Invalid username or password"
        case .invalidStudentRole:
            return "This app is only available for students"
        case .networkError(let message):
            return "Network error: \(message)"
        case .tokenExpired:
            return "Your session has expired. Please login again"
        }
    }
}

// MARK: - Student Auth Service
@MainActor
class StudentAuthService: ObservableObject {
    static let shared = StudentAuthService()

    // MARK: - Published Properties
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var authError: String?

    // MARK: - Private Properties
    private let keychainManager = KeychainManager.shared
    private let apiBaseURL = "https://lms-dev1.earnbase.io/api/v1"

    private init() {
        // Check existing auth state on initialization
        checkAuthState()
    }

    // MARK: - Authentication Methods

    /// Login with username and password
    func login(username: String, password: String, rememberMe: Bool = false) async throws {
        isLoading = true
        authError = nil

        defer { isLoading = false }

        do {
            // Validate input
            guard !username.isEmpty, !password.isEmpty else {
                throw StudentAuthError.invalidCredentials
            }

            // Create device info
            let device = UIDevice.current
            let bundle = Bundle.main

            let deviceInfo = StudentDeviceInfo(
                device_id: device.identifierForVendor?.uuidString ?? UUID().uuidString,
                device_name: device.name,
                device_type: "mobile",
                os_name: "iOS",
                os_version: device.systemVersion,
                app_version: bundle.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                browser_name: "Safari",
                browser_version: device.systemVersion,
                user_agent: "StudentApp/\(bundle.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0") iOS/\(device.systemVersion)",
                ip_address: "*************",
                location: StudentLocationInfo(
                    city: "Hanoi",
                    country: "Vietnam",
                    latitude: 21.0285,
                    longitude: 105.8542
                )
            )

            // Create login request
            let loginRequest = StudentLoginRequest(
                username: username,
                password: password,
                device_info: deviceInfo,
                remember_me: rememberMe,
                mfa_code: nil
            )

            print("🎓 StudentAuth: Attempting login for \(username)")

            // Make API request
            let response = try await performLoginRequest(loginRequest)

            print("🎓 StudentAuth: Login response received")
            print("🎓 StudentAuth: Success: \(response.success), Message: \(response.message)")

            guard response.success, let data = response.data else {
                throw StudentAuthError.invalidCredentials
            }

            // Extract user role from JWT token
            let userRole = extractRoleFromToken(data.access_token)

            // Check if user is student or instructor (temporarily allow instructor for testing)
            guard userRole == "student" || userRole == "instructor" else {
                print("🎓 StudentAuth: Invalid role: \(userRole). Only students/instructors can use this app.")
                throw StudentAuthError.invalidStudentRole
            }

            // Save session
            try saveSession(token: data.access_token, refreshToken: data.refresh_token, userId: data.user_id)

            // Create user object
            let userInfo = extractUserInfoFromToken(data.access_token)
            currentUser = User(
                id: String(data.user_id),
                email: username,
                firstName: userInfo.firstName,
                lastName: userInfo.lastName,
                phone: nil,
                role: .student,
                isActive: true,
                avatar: nil,
                dateOfBirth: nil,
                lastLoginAt: Date(),
                createdAt: Date(),
                updatedAt: Date(),
                businessName: nil,
                businessId: nil,
                category: nil,
                businessPhone: nil,
                website: nil,
                businessDescription: nil,
                businessStatus: nil,
                onboardedAt: nil
            )

            isAuthenticated = true
            print("🎓 StudentAuth: Login successful for student: \(username)")

        } catch {
            print("🎓 StudentAuth: Login failed: \(error)")
            authError = error.localizedDescription
            throw error
        }
    }

    /// Logout user
    func logout() async {
        isLoading = true
        defer { isLoading = false }

        // Clear session
        clearSession()

        // Update state
        isAuthenticated = false
        currentUser = nil
        authError = nil

        print("🎓 StudentAuth: Logout successful")
    }

    /// Check authentication state
    func checkAuthState() {
        if let token = try? keychainManager.loadAccessToken(), !token.isEmpty {
            let userRole = extractRoleFromToken(token)
            if userRole == "student" {
                isAuthenticated = true
                // Load user info from token
                let userInfo = extractUserInfoFromToken(token)
                currentUser = User(
                    id: userInfo.userId,
                    email: userInfo.email,
                    firstName: userInfo.firstName,
                    lastName: userInfo.lastName,
                    phone: nil,
                    role: .student,
                    isActive: true,
                    avatar: nil,
                    dateOfBirth: nil,
                    lastLoginAt: Date(),
                    createdAt: Date(),
                    updatedAt: Date(),
                    businessName: nil,
                    businessId: nil,
                    category: nil,
                    businessPhone: nil,
                    website: nil,
                    businessDescription: nil,
                    businessStatus: nil,
                    onboardedAt: nil
                )
                print("🎓 StudentAuth: Restored session for student")
            } else {
                clearSession()
            }
        }
    }

    // MARK: - Private Methods

    private func performLoginRequest(_ request: StudentLoginRequest) async throws -> StudentLoginResponse {
        guard let url = URL(string: "\(apiBaseURL)/auth/sign-in") else {
            throw StudentAuthError.networkError("Invalid URL")
        }

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("application/json", forHTTPHeaderField: "Accept")

        let encoder = JSONEncoder()
        urlRequest.httpBody = try encoder.encode(request)

        let (data, response) = try await URLSession.shared.data(for: urlRequest)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw StudentAuthError.networkError("Invalid response")
        }

        guard httpResponse.statusCode == 200 else {
            print("🚨 StudentAuth: HTTP Error \(httpResponse.statusCode)")
            if let responseString = String(data: data, encoding: .utf8) {
                print("🚨 StudentAuth: Error Response: \(responseString)")
            }
            throw StudentAuthError.invalidCredentials
        }

        // Debug: Print raw response
        if let responseString = String(data: data, encoding: .utf8) {
            print("🎓 StudentAuth: Raw Response: \(responseString)")
        }

        let decoder = JSONDecoder()
        do {
            return try decoder.decode(StudentLoginResponse.self, from: data)
        } catch {
            print("🚨 StudentAuth: JSON Decode Error: \(error)")
            throw StudentAuthError.networkError("Failed to parse response: \(error.localizedDescription)")
        }
    }

    private func extractRoleFromToken(_ token: String) -> String {
        // Simple JWT parsing - in production use a proper JWT library
        let parts = token.components(separatedBy: ".")
        guard parts.count == 3 else { return "" }

        let payload = parts[1]
        guard let data = Data(base64Encoded: addPadding(payload)) else { return "" }

        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let role = json["role"] as? String {
                return role
            }
        } catch {
            print("Error parsing JWT: \(error)")
        }

        return ""
    }

    private func extractUserInfoFromToken(_ token: String) -> (userId: String, email: String, firstName: String, lastName: String) {
        // Simple JWT parsing - in production use a proper JWT library
        let parts = token.components(separatedBy: ".")
        guard parts.count == 3 else { return ("", "", "", "") }

        let payload = parts[1]
        guard let data = Data(base64Encoded: addPadding(payload)) else { return ("", "", "", "") }

        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                let userId = json["user_id"] as? String ?? ""
                let email = json["email"] as? String ?? ""
                let name = json["name"] as? String ?? ""
                let nameParts = name.components(separatedBy: " ")
                let firstName = nameParts.first ?? ""
                let lastName = nameParts.count > 1 ? nameParts.dropFirst().joined(separator: " ") : ""
                return (userId, email, firstName, lastName)
            }
        } catch {
            print("Error parsing JWT: \(error)")
        }

        return ("", "", "", "")
    }

    private func addPadding(_ base64: String) -> String {
        let remainder = base64.count % 4
        if remainder > 0 {
            return base64 + String(repeating: "=", count: 4 - remainder)
        }
        return base64
    }

    private func saveSession(token: String, refreshToken: String, userId: Int) throws {
        try keychainManager.saveAccessToken(token)
        try keychainManager.saveRefreshToken(refreshToken)
        UserDefaults.standard.set(userId, forKey: "user_id")
    }

    private func clearSession() {
        try? keychainManager.deleteAccessToken()
        try? keychainManager.deleteRefreshToken()
        UserDefaults.standard.removeObject(forKey: "user_id")
    }
}

//
//  StudentQuizDetailView.swift
//  VantisEducation
//
//  Created by Student App on 31/7/25.
//

import SwiftUI

struct StudentQuizDetailView: View {
    let quiz: Quiz
    @Environment(\.dismiss) private var dismiss
    @State private var showingStartQuiz = false
    @State private var showingQuizTaking = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header Section
                    headerSection
                    
                    // Quiz Info Section
                    quizInfoSection
                    
                    // Instructions Section
                    if let description = quiz.description {
                        instructionsSection(description)
                    }
                    
                    // Time and Attempts Section
                    timeAndAttemptsSection
                    
                    // Action Section
                    actionSection
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationTitle("Chi tiết bài thi")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Đóng") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingQuizTaking) {
            QuizTakingView(quiz: quiz)
        }
        .alert("Bắt đầu làm bài", isPresented: $showingStartQuiz) {
            But<PERSON>("Hủy", role: .cancel) { }
            But<PERSON>("Bắt đầu") {
                showingQuizTaking = true
            }
        } message: {
            Text("Bạn có chắc chắn muốn bắt đầu làm bài thi này không?")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(quiz.name)
                        .font(.beVietnamPro(.bold, size: 24))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text(quiz.code)
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                QuizStatusBadge(state: quiz.state)
            }
            
            if let subjectName = quiz.subjectName {
                HStack {
                    Image(systemName: "book.closed")
                        .foregroundColor(AppConstants.Colors.primary)
                    
                    Text(subjectName)
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    if let instructorName = quiz.instructorName {
                        Text("• \(instructorName)")
                            .font(.beVietnamPro(.medium, size: 16))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Quiz Info Section
    private var quizInfoSection: some View {
        VStack(spacing: 16) {
            Text("Thông tin bài thi")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                InfoCard(
                    icon: "questionmark.circle",
                    title: "Số câu hỏi",
                    value: "\(quiz.questionCount)",
                    color: .blue
                )
                
                InfoCard(
                    icon: "target",
                    title: "Điểm tối đa",
                    value: "\(Int(quiz.maxScore))",
                    color: .green
                )
                
                InfoCard(
                    icon: "checkmark.circle",
                    title: "Điểm đạt",
                    value: "\(Int(quiz.passingScore))",
                    color: .orange
                )
                
                if let timeLimit = quiz.timeLimit {
                    InfoCard(
                        icon: "clock",
                        title: "Thời gian",
                        value: "\(timeLimit) phút",
                        color: .red
                    )
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Instructions Section
    private func instructionsSection(_ description: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Mô tả")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(description)
                .font(.beVietnamPro(.medium, size: 15))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Time and Attempts Section
    private var timeAndAttemptsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thời gian và lượt làm")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            VStack(spacing: 12) {
                if let startDate = quiz.startDate {
                    TimeInfoRow(
                        icon: "play.circle",
                        title: "Thời gian bắt đầu",
                        value: DateFormatter.displayDateTime.string(from: startDate),
                        color: .green
                    )
                }
                
                if let endDate = quiz.endDate {
                    TimeInfoRow(
                        icon: "stop.circle",
                        title: "Thời gian kết thúc",
                        value: DateFormatter.displayDateTime.string(from: endDate),
                        color: .red
                    )
                }
                
                if let attemptCount = quiz.attemptCount {
                    TimeInfoRow(
                        icon: "arrow.clockwise",
                        title: "Số lần đã làm",
                        value: "\(attemptCount) lần",
                        color: .blue
                    )
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(20)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Action Section
    private var actionSection: some View {
        VStack(spacing: 16) {
            switch quiz.state {
            case .published:
                Button(action: {
                    showingStartQuiz = true
                }) {
                    HStack {
                        Image(systemName: "play.fill")
                        Text("Bắt đầu làm bài")
                            .font(.beVietnamPro(.semiBold, size: 16))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(AppConstants.Colors.primary)
                    .cornerRadius(12)
                }
                
            case .inProgress:
                Button(action: {
                    showingQuizTaking = true
                }) {
                    HStack {
                        Image(systemName: "arrow.right.circle.fill")
                        Text("Tiếp tục làm bài")
                            .font(.beVietnamPro(.semiBold, size: 16))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(AppConstants.Colors.primary)
                    .cornerRadius(12)
                }
                
            case .completed:
                Button(action: {
                    // Show results
                }) {
                    HStack {
                        Image(systemName: "chart.bar.fill")
                        Text("Xem kết quả")
                            .font(.beVietnamPro(.semiBold, size: 16))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(AppConstants.Colors.success)
                    .cornerRadius(12)
                }
                
            default:
                Text("Bài thi chưa sẵn sàng")
                    .font(.beVietnamPro(.medium, size: 16))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(AppConstants.Colors.backgroundSecondary)
                    .cornerRadius(12)
            }
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - Supporting Views
struct InfoCard: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.beVietnamPro(.bold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(title)
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(AppConstants.Colors.backgroundSecondary)
        .cornerRadius(12)
    }
}

struct TimeInfoRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.beVietnamPro(.medium, size: 14))
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                Text(value)
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
    }
}

// MARK: - DateFormatter Extension
extension DateFormatter {
    static let displayDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "vi_VN")
        return formatter
    }()
}

// MARK: - Preview
struct StudentQuizDetailView_Previews: PreviewProvider {
    static var previews: some View {
        StudentQuizDetailView(
            quiz: Quiz(
                id: 1,
                name: "Kiểm tra Toán học - Chương 1",
                code: "MATH001",
                description: "Kiểm tra kiến thức về đại số và hình học cơ bản. Bài thi gồm 15 câu hỏi trắc nghiệm và tự luận.",
                quizType: .quiz,
                subjectId: 1,
                subjectName: "Toán học",
                classId: 1,
                className: "Lớp 10A1",
                instructorName: "Thầy Nguyễn Văn A",
                maxScore: 100,
                passingScore: 70,
                timeLimit: 60,
                isRandomized: true,
                showCorrectAnswers: false,
                state: .published,
                questionCount: 15,
                studentCount: 30,
                attemptCount: 0,
                averageScore: nil,
                passRate: nil,
                pendingGradingCount: nil,
                startDate: Date(),
                endDate: Date().addingTimeInterval(7 * 24 * 3600),
                createdAt: Date().addingTimeInterval(-2 * 24 * 3600),
                updatedAt: Date().addingTimeInterval(-24 * 3600)
            )
        )
    }
}

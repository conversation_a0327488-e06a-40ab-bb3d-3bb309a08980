//
//  StudentQuizListView.swift
//  VantisEducation
//
//  Created by Student App on 31/7/25.
//

import SwiftUI

struct StudentQuizListView: View {
    @StateObject private var viewModel = StudentQuizViewModel()
    @State private var selectedFilter: QuizFilter = .all
    @State private var searchText = ""
    @State private var selectedQuiz: Quiz?
    @State private var showingQuizDetail = false
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 0) {
                    // Header Section
                    headerSection
                    
                    // Stats Section
                    statsSection
                    
                    // Search and Filter Section
                    searchAndFilterSection
                    
                    // Content Section
                    contentSection
                    
                    Spacer(minLength: 80)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationBarHidden(true)
            .background(Color.white.ignoresSafeArea())
            .refreshable {
                await viewModel.refreshQuizzes()
            }
            .sheet(item: $selectedQuiz) { quiz in
                StudentQuizDetailView(quiz: quiz)
            }
        }
        .task {
            await viewModel.loadQuizzes()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Bài thi")
                        .font(.beVietnamPro(.bold, size: 28))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    Text("Danh sách bài thi của bạn")
                        .font(.beVietnamPro(.medium, size: 16))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                
                Spacer()
                
                Button(action: {
                    // Notification action
                }) {
                    Image(systemName: "bell")
                        .font(.title2)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
            }
        }
        .padding(.bottom, 8)
    }
    
    // MARK: - Stats Section
    private var statsSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
            StudentQuizStatCard(
                title: "Tổng số",
                value: "\(viewModel.quizzes.count)",
                icon: "doc.text",
                color: .blue
            )

            StudentQuizStatCard(
                title: "Chưa làm",
                value: "\(viewModel.pendingCount)",
                icon: "clock",
                color: .orange
            )

            StudentQuizStatCard(
                title: "Đã hoàn thành",
                value: "\(viewModel.completedCount)",
                icon: "checkmark.circle",
                color: .green
            )
        }
        .padding(.bottom, 24)
    }
    
    // MARK: - Search and Filter Section
    private var searchAndFilterSection: some View {
        VStack(spacing: 16) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Tìm kiếm bài thi...", text: $searchText)
                    .font(.beVietnamPro(.medium, size: 16))
                    .textFieldStyle(PlainTextFieldStyle())
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(AppConstants.Colors.backgroundSecondary)
            .cornerRadius(12)
            
            // Filter Tabs
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(QuizFilter.allCases, id: \.self) { filter in
                        FilterTab(
                            title: filter.title,
                            isSelected: selectedFilter == filter,
                            count: viewModel.getCount(for: filter)
                        ) {
                            selectedFilter = filter
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.bottom, 24)
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        LazyVStack(spacing: 12) {
            if viewModel.isLoading && viewModel.quizzes.isEmpty {
                ForEach(0..<5, id: \.self) { _ in
                    StudentQuizCardSkeleton()
                }
            } else if filteredQuizzes.isEmpty {
                StudentEmptyQuizView(filter: selectedFilter)
                    .padding(.top, 40)
            } else {
                ForEach(filteredQuizzes) { quiz in
                    StudentQuizCard(quiz: quiz) {
                        selectedQuiz = quiz
                        showingQuizDetail = true
                    }
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    private var filteredQuizzes: [Quiz] {
        let filtered = viewModel.quizzes.filter { quiz in
            // Apply filter
            switch selectedFilter {
            case .all:
                return true
            case .available:
                return quiz.state == .published
            case .completed:
                return quiz.state == .completed
            case .inProgress:
                return quiz.state == .inProgress
            }
        }
        
        // Apply search
        if searchText.isEmpty {
            return filtered
        } else {
            return filtered.filter { quiz in
                quiz.name.localizedCaseInsensitiveContains(searchText) ||
                quiz.code.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
}

// MARK: - Quiz Filter Enum
enum QuizFilter: CaseIterable {
    case all
    case available
    case inProgress
    case completed
    
    var title: String {
        switch self {
        case .all: return "Tất cả"
        case .available: return "Có thể làm"
        case .inProgress: return "Đang làm"
        case .completed: return "Đã hoàn thành"
        }
    }
}

// MARK: - Supporting Views
struct StudentQuizStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.beVietnamPro(.bold, size: 20))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(title)
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
}

struct StudentQuizCard: View {
    let quiz: Quiz
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text(quiz.name)
                        .font(.beVietnamPro(.semiBold, size: 16))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    QuizStatusBadge(state: quiz.state)
                }
                
                if let description = quiz.description {
                    Text(description)
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .lineLimit(2)
                }
                
                HStack {
                    Label("\(quiz.questionCount) câu", systemImage: "questionmark.circle")
                    
                    if let timeLimit = quiz.timeLimit {
                        Label("\(timeLimit) phút", systemImage: "clock")
                    }
                    
                    Spacer()
                    
                    Text("Điểm tối đa: \(Int(quiz.maxScore))")
                        .font(.beVietnamPro(.medium, size: 12))
                        .foregroundColor(AppConstants.Colors.textSecondary)
                }
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(AppConstants.Colors.textSecondary)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct StudentQuizCardSkeleton: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 20)
                    .frame(maxWidth: .infinity)
                
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 60, height: 24)
            }
            
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.3))
                .frame(height: 16)
            
            HStack {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 80, height: 14)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 60, height: 14)
                
                Spacer()
            }
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .redacted(reason: .placeholder)
    }
}

struct StudentEmptyQuizView: View {
    let filter: QuizFilter
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text")
                .font(.system(size: 48))
                .foregroundColor(AppConstants.Colors.textSecondary)
            
            Text("Không có bài thi nào")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Text(emptyMessage)
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(32)
    }
    
    private var emptyMessage: String {
        switch filter {
        case .all:
            return "Chưa có bài thi nào được giao cho bạn"
        case .available:
            return "Không có bài thi nào có thể làm"
        case .inProgress:
            return "Bạn chưa bắt đầu làm bài thi nào"
        case .completed:
            return "Bạn chưa hoàn thành bài thi nào"
        }
    }
}

// MARK: - Preview
struct StudentQuizListView_Previews: PreviewProvider {
    static var previews: some View {
        StudentQuizListView()
    }
}

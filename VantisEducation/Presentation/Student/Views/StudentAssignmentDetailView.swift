//
//  StudentAssignmentDetailView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct StudentAssignmentDetailView: View {
    let assignment: Assignment
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(alignment: .leading, spacing: 8) {
                        Text(assignment.title)
                            .font(.beVietnamPro(.bold, size: 24))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text(assignment.subject)
                            .font(.beVietnamPro(.medium, size: 16))
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                    
                    // Status and due date
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Trạng thái:")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            
                            Text(assignment.statusText)
                                .font(.beVietnamPro(.semiBold, size: 14))
                                .foregroundColor(assignment.statusColor)
                        }
                        
                        HStack {
                            Text("Hạn nộp:")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            
                            Text(assignment.dueDateFormatted)
                                .font(.beVietnamPro(.semiBold, size: 14))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                        }
                        
                        HStack {
                            Text("Thời gian còn lại:")
                                .font(.beVietnamPro(.medium, size: 14))
                                .foregroundColor(AppConstants.Colors.textSecondary)
                            
                            Text(assignment.timeRemaining)
                                .font(.beVietnamPro(.semiBold, size: 14))
                                .foregroundColor(assignment.isOverdue ? .red : AppConstants.Colors.primary)
                        }
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.05))
                    )
                    
                    // Description
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Mô tả bài tập")
                            .font(.beVietnamPro(.semiBold, size: 18))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text(assignment.description)
                            .font(.beVietnamPro(.medium, size: 15))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    // Instructions
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Hướng dẫn")
                            .font(.beVietnamPro(.semiBold, size: 18))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text(assignment.instructions ?? "Không có hướng dẫn")
                            .font(.beVietnamPro(.medium, size: 15))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                    
                    // Attachments
                    if !assignment.attachmentNames.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Tài liệu đính kèm")
                                .font(.beVietnamPro(.semiBold, size: 18))
                                .foregroundColor(AppConstants.Colors.textPrimary)

                            ForEach(assignment.attachmentNames, id: \.self) { attachment in
                                HStack {
                                    Image(systemName: "doc.fill")
                                        .foregroundColor(AppConstants.Colors.primary)
                                    
                                    Text(attachment)
                                        .font(.beVietnamPro(.medium, size: 14))
                                        .foregroundColor(AppConstants.Colors.textPrimary)
                                    
                                    Spacer()
                                    
                                    Button("Tải xuống") {
                                        // Download attachment
                                    }
                                    .font(.beVietnamPro(.medium, size: 12))
                                    .foregroundColor(AppConstants.Colors.primary)
                                }
                                .padding(12)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.gray.opacity(0.05))
                                )
                            }
                        }
                    }
                    
                    // Grade (if graded)
                    if assignment.isGraded, let grade = assignment.grade {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Kết quả")
                                .font(.beVietnamPro(.semiBold, size: 18))
                                .foregroundColor(AppConstants.Colors.textPrimary)
                            
                            HStack {
                                Text("Điểm số:")
                                    .font(.beVietnamPro(.medium, size: 16))
                                    .foregroundColor(AppConstants.Colors.textSecondary)
                                
                                Spacer()
                                
                                Text("\(String(format: "%.1f", grade))/\(String(format: "%.0f", assignment.maxGrade))")
                                    .font(.beVietnamPro(.bold, size: 20))
                                    .foregroundColor(.green)
                            }
                        }
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green.opacity(0.05))
                        )
                    }
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationTitle("Chi tiết bài tập")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "chevron.left")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        // Share assignment
                    }) {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(AppConstants.Colors.primary)
                    }
                }
            }
            .safeAreaInset(edge: .bottom) {
                if !assignment.isSubmitted && !assignment.isOverdue {
                    VStack(spacing: 12) {
                        Button(action: {
                            // Submit assignment
                        }) {
                            HStack {
                                Image(systemName: "paperplane.fill")
                                Text("Nộp bài tập")
                                    .font(.beVietnamPro(.semiBold, size: 16))
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(AppConstants.Colors.primary)
                            )
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(Color.white)
                }
            }
        }
    }
}

// MARK: - Preview
#Preview {
    StudentAssignmentDetailView(assignment: Assignment.mockStudentAssignments[0])
}

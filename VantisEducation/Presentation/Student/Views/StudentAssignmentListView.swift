//
//  StudentAssignmentListView.swift
//  VantisEducation
//
//  Created by Vantis Team on 31/7/25.
//

import SwiftUI

struct StudentAssignmentListView: View {
    @StateObject private var viewModel = StudentAssignmentViewModel()
    @State private var selectedAssignment: Assignment?
    @State private var showingAssignmentDetail = false
    @State private var searchText = ""
    @State private var selectedFilter: AssignmentFilter = .all
    
    enum AssignmentFilter: String, CaseIterable {
        case all = "Tất cả"
        case pending = "Chưa nộp"
        case submitted = "Đã nộp"
        case graded = "Đã chấm"
        case overdue = "Quá hạn"
    }
    
    var filteredAssignments: [Assignment] {
        var assignments = viewModel.assignments
        
        // Apply search filter
        if !searchText.isEmpty {
            assignments = assignments.filter { assignment in
                assignment.title.localizedCaseInsensitiveContains(searchText) ||
                assignment.description.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // Apply status filter
        switch selectedFilter {
        case .all:
            break
        case .pending:
            assignments = assignments.filter { !$0.isSubmitted && !$0.isOverdue }
        case .submitted:
            assignments = assignments.filter { $0.isSubmitted && !$0.isGraded }
        case .graded:
            assignments = assignments.filter { $0.isGraded }
        case .overdue:
            assignments = assignments.filter { $0.isOverdue }
        }
        
        return assignments
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 0) {
                    // Header Section
                    headerSection
                    
                    // Stats Section
                    statsSection
                    
                    // Search and Filter Section
                    searchAndFilterSection
                    
                    // Content Section
                    contentSection
                    
                    Spacer(minLength: 80)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .navigationBarHidden(true)
            .background(Color.white.ignoresSafeArea())
            .refreshable {
                await viewModel.refreshAssignments()
            }
            .sheet(item: $selectedAssignment) { assignment in
                StudentAssignmentDetailView(assignment: assignment)
            }
        }
        .task {
            await viewModel.loadAssignments()
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Button(action: {
                    // Navigate back
                }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .foregroundColor(AppConstants.Colors.primary)
                }
                
                Spacer()
                
                Text("Bài tập")
                    .font(.beVietnamPro(.bold, size: 24))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                Spacer()
                
                Button(action: {
                    // Filter action
                }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .font(.title2)
                        .foregroundColor(AppConstants.Colors.primary)
                }
            }
            
            Text("Quản lý và theo dõi bài tập của bạn")
                .font(.beVietnamPro(.medium, size: 16))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
    
    // MARK: - Stats Section
    private var statsSection: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
            StudentAssignmentStatCard(
                title: "Tổng số",
                value: "\(viewModel.assignments.count)",
                icon: "doc.text",
                color: .blue
            )

            StudentAssignmentStatCard(
                title: "Chưa nộp",
                value: "\(viewModel.pendingCount)",
                icon: "clock",
                color: .orange
            )

            StudentAssignmentStatCard(
                title: "Đã chấm",
                value: "\(viewModel.gradedCount)",
                icon: "checkmark.circle",
                color: .green
            )
        }
        .padding(.vertical, 16)
    }
    
    // MARK: - Search and Filter Section
    private var searchAndFilterSection: some View {
        VStack(spacing: 12) {
            // Search Bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(AppConstants.Colors.textSecondary)
                
                TextField("Tìm kiếm bài tập...", text: $searchText)
                    .font(.beVietnamPro(.medium, size: 16))
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.1))
            )
            
            // Filter Tabs
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(AssignmentFilter.allCases, id: \.self) { filter in
                        AssignmentFilterTab(
                            title: filter.rawValue,
                            isSelected: selectedFilter == filter
                        ) {
                            selectedFilter = filter
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - Content Section
    private var contentSection: some View {
        LazyVStack(spacing: 12) {
            if viewModel.isLoading && viewModel.assignments.isEmpty {
                ForEach(0..<5, id: \.self) { _ in
                    StudentAssignmentCardSkeleton()
                }
            } else if filteredAssignments.isEmpty {
                StudentEmptyAssignmentView(filter: selectedFilter)
                    .padding(.top, 40)
            } else {
                ForEach(filteredAssignments) { assignment in
                    StudentAssignmentCard(assignment: assignment) {
                        selectedAssignment = assignment
                        showingAssignmentDetail = true
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Views

struct StudentAssignmentStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.beVietnamPro(.bold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)

            Text(title)
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white)
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
        )
    }
}

struct AssignmentFilterTab: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(isSelected ? .white : AppConstants.Colors.textSecondary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? AppConstants.Colors.primary : Color.gray.opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct StudentEmptyAssignmentView: View {
    let filter: StudentAssignmentListView.AssignmentFilter

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text")
                .font(.system(size: 48))
                .foregroundColor(AppConstants.Colors.textSecondary)

            Text("Không có bài tập")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)

            Text(emptyMessage)
                .font(.beVietnamPro(.medium, size: 14))
                .foregroundColor(AppConstants.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 40)
    }
    
    private var emptyMessage: String {
        switch filter {
        case .all:
            return "Chưa có bài tập nào được giao"
        case .pending:
            return "Bạn đã hoàn thành tất cả bài tập"
        case .submitted:
            return "Chưa có bài tập nào đang chờ chấm điểm"
        case .graded:
            return "Chưa có bài tập nào được chấm điểm"
        case .overdue:
            return "Không có bài tập nào quá hạn"
        }
    }
}

// MARK: - Preview
#Preview {
    StudentAssignmentListView()
}

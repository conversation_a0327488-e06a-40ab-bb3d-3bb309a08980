//
//  StudentDashboardView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 27/7/25.
//

import SwiftUI

struct StudentDashboardView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var dashboardViewModel = StudentDashboardViewModel()
    @StateObject private var notificationsViewModel = NotificationsViewModel()
    @State private var showProfile = false
    @State private var showSettings = false
    @State private var navigateToNotifications = false
    @State private var navigateToQuizzes = false
    @State private var navigateToGrades = false
    @State private var navigateToSchedule = false
    @State private var animateCards = false

    var body: some View {
        NavigationStack {
            ScrollView {
            VStack(spacing: 20) {
                // Welcome Header with User Info
                welcomeHeader

                // Academic Progress Overview
                academicProgressSection

                // Quick Actions Grid
                quickActionsSection

                // What's Next Section (Upcoming Tasks)
                whatsNextSection

                // Recent Activity
                recentActivitySection

                // Today's Schedule
                todayScheduleSection
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)
            }
            .navigationBarHidden(true)
            .background(AppConstants.Colors.background.ignoresSafeArea())
            .refreshable {
                await refreshData()
            }
            .navigationDestination(isPresented: $navigateToNotifications) {
                NotificationsView()
                    .environmentObject(notificationsViewModel)
            }
            .navigationDestination(isPresented: $navigateToQuizzes) {
                QuizManagementView()
            }
            .navigationDestination(isPresented: $navigateToGrades) {
                StudentGradesView()
            }
            .navigationDestination(isPresented: $navigateToSchedule) {
                StudentScheduleView()
            }
        }
        .onAppear {
            withAnimation(.easeOut(duration: 0.8).delay(0.2)) {
                animateCards = true
            }
            Task {
                await dashboardViewModel.loadDashboardData()
            }
        }
        .sheet(isPresented: $showProfile) {
            StudentProfileView()
        }
        .sheet(isPresented: $showSettings) {
            StudentSettingsView()
        }
    }
    
    // MARK: - Student Header Section

    private var welcomeHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(greetingMessage)
                    .font(AppConstants.Typography.subheadline)
                    .foregroundColor(AppConstants.Colors.textSecondary)

                if let user = authViewModel.currentUser {
                    Text(user.displayName)
                        .font(AppConstants.Typography.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(AppConstants.Colors.textPrimary)

                    Text(user.email)
                        .font(AppConstants.Typography.caption)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                } else {
                    Text("Học viên")
                        .font(AppConstants.Typography.title2)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                }

                // Quick info
                HStack(spacing: 16) {
                    if authViewModel.permissionsCount > 0 {
                        HStack(spacing: 4) {
                            Image(systemName: "key.fill")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.primary)

                            Text("\(authViewModel.permissionsCount) quyền truy cập")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }

                    if authViewModel.biometricEnabled {
                        HStack(spacing: 4) {
                            Image(systemName: "shield.checkered")
                                .font(.caption)
                                .foregroundColor(AppConstants.Colors.success)

                            Text("Bảo mật đã bật")
                                .font(AppConstants.Typography.caption)
                                .foregroundColor(AppConstants.Colors.textSecondary)
                        }
                    }
                }
            }

            Spacer()

            // Notification Button
            Button(action: {
                // Add haptic feedback
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()

                navigateToNotifications = true
            }) {
                ZStack {
                    IconWithBadge(
                        iconName: notificationsViewModel.unreadCount > 0 ? "bell.badge" : "bell",
                        badgeCount: notificationsViewModel.unreadCount,
                        iconColor: notificationsViewModel.unreadCount > 0 ? AppConstants.Colors.primary : AppConstants.Colors.textPrimary,
                        iconSize: 20
                    )
                }
                .frame(width: 44, height: 44)
                .background(
                    notificationsViewModel.unreadCount > 0
                        ? AppConstants.Colors.primary.opacity(0.1)
                        : AppConstants.Colors.surface
                )
                .cornerRadius(22)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                .scaleEffect(notificationsViewModel.unreadCount > 0 ? 1.05 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: notificationsViewModel.unreadCount)
            }
        }
    }

    private var greetingMessage: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12: return "Chào buổi sáng!"
        case 12..<17: return "Chào buổi chiều!"
        case 17..<22: return "Chào buổi tối!"
        default: return "Chào bạn!"
        }
    }

    // MARK: - Helper Methods
    private func refreshData() async {
        await dashboardViewModel.refreshData()
    }

    private func timeAgoString(from date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    // MARK: - Academic Progress Section
    private var academicProgressSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Image(systemName: "person.crop.circle.fill")
                            .font(.title3)
                            .foregroundColor(AppConstants.Colors.primary)

                        Text("Tiến độ học tập")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                    }

                    Text("Tỷ lệ hoàn thành trung bình")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text(dashboardViewModel.progressPercentage)
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(AppConstants.Colors.primary)

                    HStack(spacing: 4) {
                        Image(systemName: "arrow.up")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.success)

                        Text("+2%")
                            .font(.caption)
                            .foregroundColor(AppConstants.Colors.success)
                    }
                }
            }

            // Progress Stats Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                AcademicStatCard(
                    title: "Khóa học",
                    value: "\(dashboardViewModel.activeCourses)",
                    subtitle: "Đang học",
                    icon: "book.fill",
                    color: .blue
                )

                AcademicStatCard(
                    title: "Điểm TB",
                    value: dashboardViewModel.gradeDisplay,
                    subtitle: "Trung bình",
                    icon: "star.fill",
                    color: .orange
                )
            }
        }
        .padding(16)
        .background(AppConstants.Colors.surface)
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeOut(duration: 0.8).delay(0.2), value: animateCards)
    }
    
    // MARK: - Quick Actions
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Thao tác nhanh")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                StudentQuickActionButton(
                    title: "Bài thi",
                    subtitle: "Xem & làm bài",
                    icon: "doc.text.fill",
                    color: .blue
                ) {
                    navigateToQuizzes = true
                }

                StudentQuickActionButton(
                    title: "Điểm số",
                    subtitle: "Xem kết quả",
                    icon: "star.fill",
                    color: .orange
                ) {
                    navigateToGrades = true
                }

                StudentQuickActionButton(
                    title: "Lịch học",
                    subtitle: "Xem lịch trình",
                    icon: "calendar",
                    color: .green
                ) {
                    navigateToSchedule = true
                }

                StudentQuickActionButton(
                    title: "Thông báo",
                    subtitle: "Tin nhắn mới",
                    icon: "bell.fill",
                    color: .purple
                ) {
                    navigateToNotifications = true
                }
            }
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeOut(duration: 0.8).delay(0.4), value: animateCards)
    }

    // MARK: - What's Next Section
    private var whatsNextSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Sắp tới")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Button("Xem tất cả") {
                    // Navigate to full task list
                }
                .font(.caption)
                .foregroundColor(AppConstants.Colors.primary)
            }

            if dashboardViewModel.hasUpcomingTasks {
                VStack(spacing: 12) {
                    ForEach(dashboardViewModel.upcomingAssignments.prefix(2), id: \.id) { assignment in
                        UpcomingTaskCard(
                            title: assignment.title,
                            subtitle: assignment.courseName ?? "Khóa học",
                            dueDate: assignment.dueDate,
                            type: .assignment,
                            isOverdue: assignment.dueDate < Date()
                        )
                    }

                    ForEach(dashboardViewModel.upcomingExams.prefix(1), id: \.id) { exam in
                        UpcomingTaskCard(
                            title: exam.name,
                            subtitle: exam.subjectName ?? "Khóa học",
                            dueDate: exam.startDate ?? Date(),
                            type: .exam,
                            isOverdue: false
                        )
                    }
                }
            } else {
                DashboardEmptyStateView(
                    icon: "checkmark.circle.fill",
                    title: "Tuyệt vời!",
                    subtitle: "Bạn đã hoàn thành tất cả nhiệm vụ",
                    color: .green
                )
            }
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeOut(duration: 0.8).delay(0.6), value: animateCards)
    }

    // MARK: - Today's Schedule
    private var todayScheduleSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Lịch học hôm nay")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            if !dashboardViewModel.todayClasses.isEmpty {
                VStack(spacing: 12) {
                    ForEach(dashboardViewModel.todayClasses.prefix(3), id: \.id) { classItem in
                        TodayClassCard(classItem: classItem)
                    }
                }
            } else {
                DashboardEmptyStateView(
                    icon: "calendar",
                    title: "Không có lớp học",
                    subtitle: "Hôm nay bạn không có lịch học",
                    color: .blue
                )
            }
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeOut(duration: 0.8).delay(0.8), value: animateCards)
    }

    // MARK: - Recent Activity
    
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Hoạt động gần đây")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            VStack(spacing: 12) {
                ForEach(dashboardViewModel.recentActivities.prefix(3), id: \.id) { activity in
                    ActivityRow(
                        title: activity.title,
                        subtitle: activity.description,
                        icon: activity.iconName,
                        color: activity.color,
                        time: timeAgoString(from: activity.timestamp)
                    )
                }
            }
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeOut(duration: 0.8).delay(0.8), value: animateCards)
    }
    
    // MARK: - Permissions Section (Debug)
    
    private var permissionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Tổng quan quyền truy cập")
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            VStack(alignment: .leading, spacing: 8) {
                PermissionRow(
                    title: "View Profile",
                    enabled: authViewModel.hasPermission(StudentPermission.readOwnProfile.rawValue)
                )
                
                PermissionRow(
                    title: "Edit Profile",
                    enabled: authViewModel.hasPermission(StudentPermission.updateOwnProfile.rawValue)
                )
                
                PermissionRow(
                    title: "View Courses",
                    enabled: authViewModel.hasPermission(StudentPermission.readEnrolledCourses.rawValue)
                )
                
                PermissionRow(
                    title: "Submit Attendance",
                    enabled: authViewModel.hasPermission(StudentPermission.submitAttendance.rawValue)
                )
                
                PermissionRow(
                    title: "View Grades",
                    enabled: authViewModel.hasPermission(StudentPermission.readOwnGrades.rawValue)
                )
            }
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(AppConstants.Colors.surface)
            )
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeOut(duration: 0.8).delay(1.0), value: animateCards)
    }
    

}

// MARK: - Supporting Views

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(AppConstants.Colors.textPrimary)

            Text(title)
                .font(.caption)
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppConstants.Colors.cardBackground)
                .shadow(color: AppConstants.Colors.shadow, radius: 8, x: 0, y: 2)
        )
    }
}

struct ActionCard: View {
    let title: String
    let icon: String
    let color: Color
    let enabled: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(enabled ? color : .gray)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(enabled ? AppConstants.Colors.textPrimary : AppConstants.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(enabled ? AppConstants.Colors.cardBackground : AppConstants.Colors.surface)
                    .shadow(color: AppConstants.Colors.shadow, radius: 8, x: 0, y: 2)
            )
        }
        .disabled(!enabled)
    }
}

struct ActivityRow: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let time: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(color)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(time)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 16)
    }
}

struct PermissionRow: View {
    let title: String
    let enabled: Bool
    
    var body: some View {
        HStack {
            Image(systemName: enabled ? "checkmark.circle.fill" : "xmark.circle.fill")
                .font(.system(size: 16))
                .foregroundColor(enabled ? .green : .red)
            
            Text(title)
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Spacer()
        }
        .padding(.horizontal, 16)
    }
}

// MARK: - Custom UI Components

struct AcademicStatCard: View {
    let title: String
    let value: String
    let subtitle: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)

                Spacer()
            }

            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(12)
        .background(AppConstants.Colors.background)
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
}

struct StudentQuickActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 32, height: 32)

                VStack(spacing: 2) {
                    Text(title)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(AppConstants.Colors.textPrimary)
                        .multilineTextAlignment(.center)

                    Text(subtitle)
                        .font(.caption2)
                        .foregroundColor(AppConstants.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(AppConstants.Colors.surface)
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    StudentDashboardView()
        .environmentObject(AuthViewModel())
}

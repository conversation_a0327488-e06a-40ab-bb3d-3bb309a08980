//
//  StudentQuizViewModel.swift
//  VantisEducation
//
//  Created by Student App on 31/7/25.
//

import Foundation
import SwiftUI

@MainActor
class StudentQuizViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var quizzes: [Quiz] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showingError = false
    
    // MARK: - Computed Properties
    var pendingCount: Int {
        quizzes.filter { $0.state == .published }.count
    }
    
    var completedCount: Int {
        quizzes.filter { $0.state == .completed }.count
    }
    
    var inProgressCount: Int {
        quizzes.filter { $0.state == .inProgress }.count
    }
    
    // MARK: - Initialization
    init() {
        loadMockData()
    }
    
    // MARK: - Public Methods
    func loadQuizzes() async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Simulate API call
            try await Task.sleep(nanoseconds: 800_000_000) // 0.8 seconds
            
            // In real implementation, this would call API
            // For now, use mock data
            loadMockData()
            
        } catch {
            errorMessage = "Không thể tải danh sách bài thi: \(error.localizedDescription)"
            showingError = true
        }
        
        isLoading = false
    }
    
    func refreshQuizzes() async {
        await loadQuizzes()
    }
    
    func getCount(for filter: QuizFilter) -> Int {
        switch filter {
        case .all:
            return quizzes.count
        case .available:
            return pendingCount
        case .inProgress:
            return inProgressCount
        case .completed:
            return completedCount
        }
    }
    
    // MARK: - Private Methods
    private func loadMockData() {
        quizzes = [
            Quiz(
                id: 1,
                name: "Kiểm tra Toán học - Chương 1",
                code: "MATH001",
                description: "Kiểm tra kiến thức về đại số và hình học cơ bản",
                quizType: .quiz,
                subjectId: 1,
                subjectName: "Toán học",
                classId: 1,
                className: "Lớp 10A1",
                instructorName: "Thầy Nguyễn Văn A",
                maxScore: 100,
                passingScore: 70,
                timeLimit: 60,
                isRandomized: true,
                showCorrectAnswers: false,
                state: .published,
                questionCount: 15,
                studentCount: 30,
                attemptCount: 0,
                averageScore: nil,
                passRate: nil,
                pendingGradingCount: nil,
                startDate: Date(),
                endDate: Date().addingTimeInterval(7 * 24 * 3600), // 7 days from now
                createdAt: Date().addingTimeInterval(-2 * 24 * 3600), // 2 days ago
                updatedAt: Date().addingTimeInterval(-24 * 3600) // 1 day ago
            ),
            Quiz(
                id: 2,
                name: "Bài thi Văn học - Truyện Kiều",
                code: "LIT002",
                description: "Phân tích tác phẩm Truyện Kiều của Nguyễn Du",
                quizType: .exam,
                subjectId: 2,
                subjectName: "Văn học",
                classId: 1,
                className: "Lớp 10A1",
                instructorName: "Cô Trần Thị B",
                maxScore: 50,
                passingScore: 30,
                timeLimit: 90,
                isRandomized: false,
                showCorrectAnswers: true,
                state: .inProgress,
                questionCount: 8,
                studentCount: 30,
                attemptCount: 1,
                averageScore: nil,
                passRate: nil,
                pendingGradingCount: nil,
                startDate: Date().addingTimeInterval(-24 * 3600), // 1 day ago
                endDate: Date().addingTimeInterval(2 * 24 * 3600), // 2 days from now
                createdAt: Date().addingTimeInterval(-3 * 24 * 3600), // 3 days ago
                updatedAt: Date().addingTimeInterval(-24 * 3600) // 1 day ago
            ),
            Quiz(
                id: 3,
                name: "Kiểm tra Tiếng Anh - Unit 5",
                code: "ENG003",
                description: "Kiểm tra từ vựng và ngữ pháp Unit 5",
                quizType: .quiz,
                subjectId: 3,
                subjectName: "Tiếng Anh",
                classId: 1,
                className: "Lớp 10A1",
                instructorName: "Ms. Johnson",
                maxScore: 80,
                passingScore: 56,
                timeLimit: 45,
                isRandomized: true,
                showCorrectAnswers: false,
                state: .completed,
                questionCount: 20,
                studentCount: 30,
                attemptCount: 1,
                averageScore: 75.5,
                passRate: 85.0,
                pendingGradingCount: 0,
                startDate: Date().addingTimeInterval(-7 * 24 * 3600), // 7 days ago
                endDate: Date().addingTimeInterval(-3 * 24 * 3600), // 3 days ago
                createdAt: Date().addingTimeInterval(-10 * 24 * 3600), // 10 days ago
                updatedAt: Date().addingTimeInterval(-3 * 24 * 3600) // 3 days ago
            ),
            Quiz(
                id: 4,
                name: "Bài tập Vật lý - Chương 2",
                code: "PHY004",
                description: "Bài tập về chuyển động thẳng đều và chuyển động thẳng biến đổi đều",
                quizType: .assignment,
                subjectId: 4,
                subjectName: "Vật lý",
                classId: 1,
                className: "Lớp 10A1",
                instructorName: "Thầy Lê Văn C",
                maxScore: 60,
                passingScore: 36,
                timeLimit: nil,
                isRandomized: false,
                showCorrectAnswers: true,
                state: .published,
                questionCount: 10,
                studentCount: 30,
                attemptCount: 0,
                averageScore: nil,
                passRate: nil,
                pendingGradingCount: nil,
                startDate: Date().addingTimeInterval(24 * 3600), // 1 day from now
                endDate: Date().addingTimeInterval(5 * 24 * 3600), // 5 days from now
                createdAt: Date().addingTimeInterval(-24 * 3600), // 1 day ago
                updatedAt: Date().addingTimeInterval(-12 * 3600) // 12 hours ago
            ),
            Quiz(
                id: 5,
                name: "Kiểm tra Hóa học - Bảng tuần hoàn",
                code: "CHEM005",
                description: "Kiểm tra kiến thức về bảng tuần hoàn các nguyên tố hóa học",
                quizType: .quiz,
                subjectId: 5,
                subjectName: "Hóa học",
                classId: 1,
                className: "Lớp 10A1",
                instructorName: "Cô Phạm Thị D",
                maxScore: 100,
                passingScore: 65,
                timeLimit: 50,
                isRandomized: true,
                showCorrectAnswers: false,
                state: .published,
                questionCount: 12,
                studentCount: 30,
                attemptCount: 0,
                averageScore: nil,
                passRate: nil,
                pendingGradingCount: nil,
                startDate: Date().addingTimeInterval(2 * 24 * 3600), // 2 days from now
                endDate: Date().addingTimeInterval(9 * 24 * 3600), // 9 days from now
                createdAt: Date().addingTimeInterval(-24 * 3600), // 1 day ago
                updatedAt: Date().addingTimeInterval(-6 * 3600) // 6 hours ago
            )
        ]
    }
}

//
//  StudentCoursesViewModel.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 29/7/25.
//

import Foundation
import Combine
import SwiftUI

// MARK: - Student Courses View Model
@MainActor
class StudentCoursesViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var courses: [StudentCourse] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showError = false
    
    // Filter and Sort
    @Published var selectedFilter: CourseFilterStatus = .all
    @Published var selectedSort: CourseSortOption = .enrollmentDate
    @Published var sortOrder: CoursesSortOrder = .desc
    @Published var searchText = ""
    
    // Pagination
    @Published var currentPage = 1
    @Published var totalPages = 1
    @Published var totalCount = 0
    @Published var hasMorePages = false

    // State tracking
    private var hasInitialized = false
    
    // MARK: - Private Properties
    private let coursesService = StudentCoursesService.shared
    private var cancellables = Set<AnyCancellable>()
    private let pageSize = 20
    
    // MARK: - Computed Properties
    var filteredCourses: [StudentCourse] {
        var filtered = courses
        
        // Apply search filter
        if !searchText.isEmpty {
            filtered = filtered.filter { course in
                course.name.localizedCaseInsensitiveContains(searchText) ||
                course.code.localizedCaseInsensitiveContains(searchText) ||
                course.category?.localizedCaseInsensitiveContains(searchText) == true ||
                course.subjectName?.localizedCaseInsensitiveContains(searchText) == true
            }
        }
        
        return filtered
    }
    
    var currentCourses: [StudentCourse] {
        filteredCourses.filter { $0.isCurrent }
    }
    
    var completedCourses: [StudentCourse] {
        filteredCourses.filter { $0.isCompleted }
    }
    
    var needsAttentionCourses: [StudentCourse] {
        filteredCourses.filter { $0.needsAttention }
    }
    
    // MARK: - Initialization
    init() {
        setupBindings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Auto-refresh when filter changes (but not on initial setup)
        Publishers.CombineLatest3($selectedFilter, $selectedSort, $sortOrder)
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .sink { [weak self] _, _, _ in
                guard let self = self, self.hasInitialized else { return }
                Task {
                    await self.loadCourses(refresh: true)
                }
            }
            .store(in: &cancellables)
        
        // Auto-search when search text changes
        $searchText
            .debounce(for: .milliseconds(500), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                // Search is handled by computed property, no need to reload
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods

    /// Initialize courses data - called when tab is first selected
    func initializeCourses() async {
        // Only load if not already initialized
        guard !hasInitialized else {
            print("🎯 StudentCoursesViewModel: Already initialized, skipping")
            return
        }

        print("🎯 StudentCoursesViewModel: Initializing courses data...")
        await loadCourses(refresh: true)
    }

    func loadCourses(refresh: Bool = false) async {
        // Prevent duplicate calls
        guard !isLoading else {
            print("🚫 StudentCoursesViewModel: Already loading, skipping duplicate call")
            return
        }

        if refresh {
            currentPage = 1
            courses.removeAll()
        }

        isLoading = true
        errorMessage = nil
        showError = false

        // Mark as initialized after first load
        defer { hasInitialized = true }
        
        do {
            print("📱 StudentCoursesViewModel: Loading courses from API...")
            print("🔍 FULL REQUEST LOGGING ENABLED")
            print(String(repeating: "=", count: 60))

            // Log request parameters
            print("📤 REQUEST PARAMETERS:")
            print("   - Filter: \(selectedFilter == .all ? "all" : selectedFilter.rawValue)")
            print("   - Page: \(currentPage)")
            print("   - Page Size: \(pageSize)")
            print("   - Sort By: \(selectedSort.rawValue)")
            print("   - Sort Order: \(sortOrder.rawValue)")

            let startTime = Date()

            // Use Combine to async/await conversion
            let response = try await withCheckedThrowingContinuation { continuation in
                coursesService.getEnrolledCourses(
                    status: selectedFilter == .all ? nil : selectedFilter.rawValue,
                    page: currentPage,
                    pageSize: pageSize,
                    sortBy: selectedSort.rawValue,
                    sortOrder: sortOrder.rawValue
                )
                .sink(
                    receiveCompletion: { completion in
                        if case .failure(let error) = completion {
                            continuation.resume(throwing: error)
                        }
                    },
                    receiveValue: { response in
                        continuation.resume(returning: response)
                    }
                )
                .store(in: &cancellables)
            }

            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)

            print("\n📥 FULL RESPONSE RECEIVED:")
            print("   - Duration: \(String(format: "%.3f", duration))s")
            print("   - Success: \(response.success)")
            print("   - Message: \(response.message)")
            print("   - Data Count: \(response.data.count)")
            print("   - Total Count: \(response.pagination.totalCount)")
            print("   - Has Next: \(response.pagination.hasNext)")
            print("   - Has Previous: \(response.pagination.hasPrevious)")
            print("   - Current Page: \(response.pagination.page)")
            print("   - Total Pages: \(response.pagination.totalPages)")

            print("\n✅ StudentCoursesViewModel: Successfully loaded \(response.data.count) courses")
            print("📋 DETAILED COURSE BREAKDOWN:")
            for (index, course) in response.data.enumerated() {
                print("  \(index + 1). Course Details:")
                print("     🆔 ID: \(course.id)")
                print("     📝 Name: \(course.name)")
                print("     🏷️ Code: \(course.code)")
                print("     🎓 Enrollment ID: \(course.enrollmentId)")
                print("     📊 Status: \(course.enrollmentStatus.displayName)")
                print("     📈 Progress: \(course.progressPercentage)%")
                print("     📊 Attendance: \(course.attendanceRate)%")
                print("     ⭐ Current: \(course.isCurrent)")
                print("     ✅ Completed: \(course.isCompleted)")
                print("     " + String(repeating: "-", count: 40))
            }

            await MainActor.run {
                if refresh {
                    courses = response.data
                } else {
                    courses.append(contentsOf: response.data)
                }

                // Update pagination info
                totalPages = response.pagination.totalPages
                totalCount = response.pagination.totalCount
                hasMorePages = currentPage < totalPages

                isLoading = false
            }

        } catch {
            print("❌ StudentCoursesViewModel: Failed to load courses - \(error.localizedDescription)")

            // Enhanced error analysis
            await MainActor.run {
                isLoading = false

                // Detailed error breakdown
                var detailedError = "❌ DETAILED ERROR ANALYSIS:\n"
                detailedError += "Error Type: \(type(of: error))\n"
                detailedError += "Description: \(error.localizedDescription)\n"

                if let decodingError = error as? DecodingError {
                    detailedError += "\n🔍 DECODING ERROR DETAILS:\n"
                    switch decodingError {
                    case .dataCorrupted(let context):
                        detailedError += "- Data corrupted at: \(context.codingPath)\n"
                        detailedError += "- Debug description: \(context.debugDescription)\n"
                    case .keyNotFound(let key, let context):
                        detailedError += "- Key not found: '\(key.stringValue)'\n"
                        detailedError += "- At path: \(context.codingPath)\n"
                        detailedError += "- Debug description: \(context.debugDescription)\n"
                    case .typeMismatch(let type, let context):
                        detailedError += "- Type mismatch: expected \(type)\n"
                        detailedError += "- At path: \(context.codingPath)\n"
                        detailedError += "- Debug description: \(context.debugDescription)\n"
                    case .valueNotFound(let type, let context):
                        detailedError += "- Value not found: expected \(type)\n"
                        detailedError += "- At path: \(context.codingPath)\n"
                        detailedError += "- Debug description: \(context.debugDescription)\n"
                    @unknown default:
                        detailedError += "- Unknown decoding error\n"
                    }
                }

                if let networkError = error as? NetworkError {
                    detailedError += "\n🌐 NETWORK ERROR DETAILS:\n"
                    detailedError += "- Network error: \(networkError)\n"
                }

                if let urlError = error as? URLError {
                    detailedError += "\n🔗 URL ERROR DETAILS:\n"
                    detailedError += "- Code: \(urlError.code.rawValue)\n"
                    detailedError += "- Description: \(urlError.localizedDescription)\n"
                }

                print(detailedError)
                errorMessage = detailedError
                showError = true
            }
        }
    }
    
    func loadMoreCourses() async {
        guard !isLoading && hasMorePages else { return }
        
        currentPage += 1
        await loadCourses(refresh: false)
    }
    
    func refreshCourses() async {
        await loadCourses(refresh: true)
    }
    
    // MARK: - Filter Actions
    func applyFilter(_ filter: CourseFilterStatus) {
        selectedFilter = filter
    }
    
    func applySorting(_ sort: CourseSortOption, order: CoursesSortOrder) {
        selectedSort = sort
        sortOrder = order
    }
    
    func clearSearch() {
        searchText = ""
    }
    
    // MARK: - Course Actions
    func getCourseById(_ id: Int) -> StudentCourse? {
        return courses.first { $0.id == id }
    }
    
    func canAccessCourse(_ course: StudentCourse) -> Bool {
        return course.canAccess && course.paymentStatus == .paid
    }
    


    // MARK: - API Testing
    func testAPIConnection() async {
        print("🔍 StudentCoursesViewModel: Testing API connection...")
        isLoading = true
        errorMessage = nil
        showError = false

        // First check if we have a token
        guard let token = SecureTokenManager.shared.getToken() else {
            print("❌ StudentCoursesViewModel: No token found")
            errorMessage = "Không tìm thấy token. Hãy inject token trước."
            showError = true
            isLoading = false
            return
        }

        print("✅ StudentCoursesViewModel: Token found: \(String(token.prefix(20)))...")

        do {
            let response = try await coursesService.getEnrolledCourses(
                page: 1,
                pageSize: 1
            ).async()

            print("✅ StudentCoursesViewModel: API connection successful")
            print("📊 StudentCoursesViewModel: Total count available: \(response.pagination.totalCount)")
            print("📋 StudentCoursesViewModel: Response message: \(response.message)")

            // Show success message
            errorMessage = "Kết nối API thành công! Tìm thấy \(response.pagination.totalCount) khóa học."
            showError = true

        } catch {
            print("❌ StudentCoursesViewModel: API connection failed - \(error.localizedDescription)")

            // More detailed error info
            if let networkError = error as? NetworkError {
                print("🔍 StudentCoursesViewModel: NetworkError details: \(networkError)")
            }

            errorMessage = "Lỗi kết nối API: \(error.localizedDescription)"
            showError = true
        }

        isLoading = false
    }

    // MARK: - Development Helper
    #if DEBUG
    func injectTestToken() {
        let testToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HUqzVzr4OO6cyjChk4WIJHjOclFQHCcKWiM5nxMtVW4"

        do {
            try SecureTokenManager.shared.saveToken(testToken)
            print("✅ StudentCoursesViewModel: Test token injected successfully")

            // Show success message
            errorMessage = "Token test đã được inject thành công!"
            showError = true
        } catch {
            print("❌ StudentCoursesViewModel: Failed to inject test token: \(error)")
            errorMessage = "Lỗi inject token: \(error.localizedDescription)"
            showError = true
        }
    }

    func debugAPICall() async {
        print("🔍 StudentCoursesViewModel: Starting FULL DEBUG API call...")
        print(String(repeating: "=", count: 80))

        // Check token first
        guard let token = SecureTokenManager.shared.getToken() else {
            print("❌ No token found")
            return
        }

        print("✅ Token found: \(String(token.prefix(20)))...")
        print("🔑 Full Token: \(token)")

        // Test direct API call with full logging
        let endpoint = "/students/courses/?page=1&page_size=10"

        print("\n📤 REQUEST DETAILS:")
        print("🌐 Base URL: \(APIConfiguration.shared.baseURL)")
        print("🛤️ Endpoint: \(endpoint)")
        print("🔗 Full URL: \(APIConfiguration.shared.baseURL)\(endpoint)")
        print("📋 Method: GET")
        print("🏷️ Headers:")
        print("   - Authorization: Bearer \(String(token.prefix(20)))...")
        print("   - Accept-Language: vi")
        print("   - Content-Type: application/json")
        print("   - Accept: application/json")

        do {
            let startTime = Date()

            // Get raw JSON response first
            let rawResponse = try await APIClient.shared.requestRaw(
                endpoint: endpoint,
                method: .GET,
                headers: ["Accept-Language": "vi"],
                requiresAuth: true
            )

            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)

            print("\n📥 RESPONSE DETAILS:")
            print("⏱️ Response Time: \(String(format: "%.3f", duration))s")
            print("📊 Response Size: \(rawResponse.count) bytes")
            print("✅ Raw API call successful!")

            print("\n📄 FULL RAW JSON RESPONSE:")
            print(String(repeating: "-", count: 80))
            if let jsonString = String(data: rawResponse, encoding: .utf8) {
                print(jsonString)
            } else {
                print("❌ Could not convert response to string")
            }
            print(String(repeating: "-", count: 80))

            // Now try to decode it
            print("\n🔄 Attempting to decode response...")
            let response: StudentCourseListResponse = try await APIClient.shared.request(
                endpoint: endpoint,
                method: .GET,
                headers: ["Accept-Language": "vi"],
                responseType: StudentCourseListResponse.self,
                requiresAuth: true
            )

            print("\n✅ DECODED RESPONSE ANALYSIS:")
            print("📊 Response success: \(response.success)")
            print("📊 Response message: \(response.message)")
            print("📊 Courses count: \(response.data.count)")
            print("📊 Pagination - Page: \(response.pagination.page)")
            print("📊 Pagination - Page Size: \(response.pagination.pageSize)")
            print("📊 Pagination - Total Count: \(response.pagination.totalCount)")
            print("📊 Pagination - Total Pages: \(response.pagination.totalPages)")
            print("📊 Pagination - Has Next: \(response.pagination.page < response.pagination.totalPages)")
            print("📊 Pagination - Has Previous: \(response.pagination.page > 1)")

            // Print detailed course info
            print("\n📋 DETAILED COURSE BREAKDOWN:")
            for (index, course) in response.data.enumerated() {
                print("  \(index + 1). Course Details:")
                print("     🆔 Course ID: \(course.id)")
                print("     📝 Name: \(course.name)")
                print("     🏷️ Code: \(course.code)")
                print("     📄 Description: \(course.description ?? "N/A")")
                print("     📋 Short Description: \(course.shortDescription ?? "N/A")")
                print("     📊 Level: \(course.level ?? "N/A")")
                print("     ⏰ Duration Hours: \(course.durationHours ?? 0)")
                print("     📚 Subject: \(course.subjectName ?? "N/A")")
                print("     🏷️ Category: \(course.category ?? "N/A")")
                print("     💰 Original Price: \(course.originalPrice ?? 0)")
                print("     💸 Discounted Price: \(course.discountedPrice ?? 0)")
                print("     🎯 Has Discount: \(course.hasDiscount)")
                print("     💱 Currency: \(course.currencyCode)")
                print("     🎓 Enrollment ID: \(course.enrollmentId)")
                print("     📅 Enrollment Date: \(course.enrollmentDate)")
                print("     📊 Enrollment Status: \(course.enrollmentStatus.displayName)")
                print("     💳 Payment Status: \(course.paymentStatus.displayName)")
                print("     🏫 Has Class: \(course.hasClass)")
                print("     🆔 Class ID: \(course.classId ?? 0)")
                print("     📝 Class Name: \(course.className ?? "N/A")")
                print("     📊 Class Status: \(course.classStatus ?? "N/A")")
                print("     📈 Progress: \(course.progressPercentage)%")
                print("     📊 Attendance Rate: \(course.attendanceRate)%")
                print("     🎯 Current Grade: \(course.currentGrade ?? "N/A")")
                print("     ⭐ Is Current: \(course.isCurrent)")
                print("     ✅ Is Completed: \(course.isCompleted)")
                print("     🔓 Can Access: \(course.canAccess)")
                print("     ⚠️ Needs Attention: \(course.needsAttention)")
                print("     📝 Attention Reason: \(course.attentionReason ?? "N/A")")
                print("     📅 Created At: \(course.createdAt)")
                print("     📅 Updated At: \(course.updatedAt)")
                print("     " + String(repeating: "-", count: 60))
            }

        } catch {
            print("\n❌ API CALL FAILED:")
            print("🔍 Error: \(error)")
            print("🔍 Error Description: \(error.localizedDescription)")

            if let networkError = error as? NetworkError {
                print("🔍 NetworkError details: \(networkError)")
            }
            if let decodingError = error as? DecodingError {
                print("🔍 DecodingError details: \(decodingError)")
                switch decodingError {
                case .dataCorrupted(let context):
                    print("   - Data corrupted: \(context)")
                case .keyNotFound(let key, let context):
                    print("   - Key not found: \(key) in \(context)")
                case .typeMismatch(let type, let context):
                    print("   - Type mismatch: \(type) in \(context)")
                case .valueNotFound(let type, let context):
                    print("   - Value not found: \(type) in \(context)")
                @unknown default:
                    print("   - Unknown decoding error")
                }
            }
            if let urlError = error as? URLError {
                print("🔍 URLError details: \(urlError)")
                print("   - Code: \(urlError.code)")
                print("   - Description: \(urlError.localizedDescription)")
            }
        }

        print("\n" + String(repeating: "=", count: 80))
        print("🔍 FULL DEBUG API CALL COMPLETED")
    }

    func testSimpleLoad() async {
        print("🔍 StudentCoursesViewModel: Testing simple course load...")

        await loadCourses(refresh: true)

        print("📊 Loaded courses count: \(courses.count)")
        print("📋 Course Names from ViewModel:")
        for (index, course) in courses.enumerated() {
            print("  \(index + 1). \(course.name)")
        }
    }

    func testRawJSONAnalysis() async {
        print("🔍 StudentCoursesViewModel: Testing RAW JSON vs Model Analysis...")

        guard let token = SecureTokenManager.shared.getToken() else {
            print("❌ No token found")
            return
        }

        let endpoint = "/students/courses/?page=1&page_size=2"

        do {
            // Get raw JSON first
            let rawData = try await APIClient.shared.requestRaw(
                endpoint: endpoint,
                method: .GET,
                headers: ["Accept-Language": "vi"],
                requiresAuth: true
            )

            print("\n📄 RAW JSON RESPONSE:")
            if let jsonString = String(data: rawData, encoding: .utf8) {
                print(jsonString)

                // Try to parse as generic JSON to see structure
                if let jsonObject = try? JSONSerialization.jsonObject(with: rawData, options: []) as? [String: Any] {
                    print("\n🔍 JSON STRUCTURE ANALYSIS:")
                    print("- Top level keys: \(Array(jsonObject.keys))")

                    if let pagination = jsonObject["pagination"] as? [String: Any] {
                        print("- Pagination keys: \(Array(pagination.keys))")
                        print("- Pagination values: \(pagination)")
                    }

                    if let data = jsonObject["data"] as? [[String: Any]], let firstCourse = data.first {
                        print("- First course keys: \(Array(firstCourse.keys))")
                        print("- Sample course data: \(firstCourse)")
                    }
                }
            }

            // Now try to decode with our model
            print("\n🔄 ATTEMPTING MODEL DECODING:")
            let response: StudentCourseListResponse = try await APIClient.shared.request(
                endpoint: endpoint,
                method: .GET,
                headers: ["Accept-Language": "vi"],
                responseType: StudentCourseListResponse.self,
                requiresAuth: true
            )

            print("✅ Model decoding successful!")
            print("📊 Decoded courses: \(response.data.count)")

        } catch {
            print("\n❌ ERROR OCCURRED:")
            print("Error: \(error)")

            if let decodingError = error as? DecodingError {
                print("\n🔍 DECODING ERROR ANALYSIS:")
                switch decodingError {
                case .keyNotFound(let key, let context):
                    print("- Missing key: '\(key.stringValue)'")
                    print("- At coding path: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                    print("- Context: \(context.debugDescription)")
                case .typeMismatch(let type, let context):
                    print("- Type mismatch: expected \(type)")
                    print("- At coding path: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                    print("- Context: \(context.debugDescription)")
                case .valueNotFound(let type, let context):
                    print("- Value not found: expected \(type)")
                    print("- At coding path: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                    print("- Context: \(context.debugDescription)")
                case .dataCorrupted(let context):
                    print("- Data corrupted")
                    print("- At coding path: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                    print("- Context: \(context.debugDescription)")
                @unknown default:
                    print("- Unknown decoding error")
                }
            }
        }
    }

    func testEnrollmentsEndpoint() async {
        print("🔍 StudentCoursesViewModel: Testing enrollments endpoint...")

        // Check token first
        guard let token = SecureTokenManager.shared.getToken() else {
            print("❌ No token found")
            return
        }

        print("✅ Token found: \(String(token.prefix(20)))...")

        // Test enrollments endpoint
        let endpoint = "/students/enrollments/?page=1&page_size=5"

        do {
            // Get raw JSON response first
            let rawResponse = try await APIClient.shared.requestRaw(
                endpoint: endpoint,
                method: .GET,
                headers: ["Accept-Language": "vi"],
                requiresAuth: true
            )

            print("✅ Enrollments endpoint successful!")
            print("📊 Raw JSON Response:")
            if let jsonString = String(data: rawResponse, encoding: .utf8) {
                print(jsonString)
            }

        } catch {
            print("❌ Enrollments endpoint failed: \(error)")
            if let networkError = error as? NetworkError {
                print("🔍 NetworkError details: \(networkError)")
            }
        }
    }
    #endif
}

// MARK: - Publisher Extension
extension Publisher {
    func async() async throws -> Output {
        try await withCheckedThrowingContinuation { continuation in
            var cancellable: AnyCancellable?
            cancellable = first()
                .sink(
                    receiveCompletion: { completion in
                        switch completion {
                        case .finished:
                            break
                        case .failure(let error):
                            continuation.resume(throwing: error)
                        }
                        cancellable?.cancel()
                    },
                    receiveValue: { value in
                        continuation.resume(returning: value)
                        cancellable?.cancel()
                    }
                )
        }
    }
}

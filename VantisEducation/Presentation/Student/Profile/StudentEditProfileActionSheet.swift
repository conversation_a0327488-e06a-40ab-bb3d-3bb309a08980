//
//  StudentEditProfileActionSheet.swift
//  VantisEducation
//
//  Created by Augment Agent on 31/7/25.
//

import SwiftUI

struct StudentEditProfileActionSheet: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var phone = ""
    @State private var dateOfBirth = Date()
    @State private var showDatePicker = false
    @State private var isLoading = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var showSuccess = false
    @FocusState private var focusedField: EditProfileField?
    
    enum EditProfileField {
        case firstName, lastName, phone
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Handle bar
                handleBar
                
                // Header
                headerSection
                
                // Content
                ScrollView {
                    VStack(spacing: 24) {
                        // Avatar section
                        avatarSection
                        
                        // Form fields
                        formSection
                        
                        // Save button
                        saveButtonSection
                        
                        Spacer(minLength: 20)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 16)
                }
            }
            .background(Color(.systemBackground))
            .navigationBarHidden(true)
        }
        .onAppear {
            loadCurrentUserData()
        }
        .alert("Lỗi", isPresented: $showError) {
            Button("Đồng ý") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
        .alert("Thành công", isPresented: $showSuccess) {
            Button("Đồng ý") {
                dismiss()
            }
        } message: {
            Text("Cập nhật hồ sơ thành công!")
        }
        .sheet(isPresented: $showDatePicker) {
            DatePickerSheet(selectedDate: $dateOfBirth)
        }
    }
    
    // MARK: - Handle Bar
    private var handleBar: some View {
        RoundedRectangle(cornerRadius: 2.5)
            .fill(Color(.systemGray4))
            .frame(width: 40, height: 5)
            .padding(.top, 8)
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Button("Hủy") {
                dismiss()
            }
            .foregroundColor(AppConstants.Colors.primary)
            
            Spacer()
            
            Text("Chỉnh sửa hồ sơ")
                .font(.beVietnamPro(.semiBold, size: 18))
                .foregroundColor(AppConstants.Colors.textPrimary)
            
            Spacer()
            
            // Invisible button for balance
            Button("Hủy") {
                // Empty
            }
            .opacity(0)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Avatar Section
    private var avatarSection: some View {
        VStack(spacing: 12) {
            ZStack {
                // Avatar background
                Circle()
                    .fill(Color(.systemGray6))
                    .frame(width: 80, height: 80)
                
                // Avatar display
                if let avatarUrl = authViewModel.currentUser?.avatar {
                    AsyncImage(url: URL(string: avatarUrl)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        ZStack {
                            LinearGradient(
                                gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                            
                            Text(authViewModel.currentUser?.initials ?? "LV")
                                .font(.beVietnamPro(.bold, size: 24))
                                .foregroundColor(.white)
                        }
                    }
                    .frame(width: 80, height: 80)
                    .clipShape(Circle())
                } else {
                    ZStack {
                        LinearGradient(
                            gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        
                        Text(authViewModel.currentUser?.initials ?? "LV")
                            .font(.beVietnamPro(.bold, size: 24))
                            .foregroundColor(.white)
                    }
                    .frame(width: 80, height: 80)
                    .clipShape(Circle())
                }
                
                // Camera icon overlay
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: {
                            // TODO: Implement photo picker
                        }) {
                            Image(systemName: "camera.fill")
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white)
                                .frame(width: 24, height: 24)
                                .background(AppConstants.Colors.primary)
                                .clipShape(Circle())
                        }
                        .offset(x: -4, y: -4)
                    }
                }
            }
            
            Text("Nhấn để thay đổi ảnh")
                .font(.beVietnamPro(.medium, size: 12))
                .foregroundColor(AppConstants.Colors.textSecondary)
        }
    }
    
    // MARK: - Form Section
    private var formSection: some View {
        VStack(spacing: 20) {
            // Personal Information
            VStack(alignment: .leading, spacing: 16) {
                Text("Thông tin cá nhân")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                VStack(spacing: 16) {
                    // First Name
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Tên")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        TextField("Lê", text: $firstName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .focused($focusedField, equals: .firstName)
                    }
                    
                    // Last Name
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Họ")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        TextField("Văn Hùng", text: $lastName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .focused($focusedField, equals: .lastName)
                    }
                    
                    // Birth Date
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Ngày sinh")
                            .font(.beVietnamPro(.medium, size: 14))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Button(action: {
                            showDatePicker = true
                        }) {
                            HStack {
                                Text(dateOfBirth.formatted(date: .abbreviated, time: .omitted))
                                    .foregroundColor(AppConstants.Colors.textPrimary)
                                
                                Spacer()
                                
                                Image(systemName: "calendar")
                                    .foregroundColor(AppConstants.Colors.primary)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                        }
                    }
                }
            }
            .padding(16)
            .background(Color(.systemGray6).opacity(0.3))
            .cornerRadius(12)
            
            // Contact Information
            VStack(alignment: .leading, spacing: 16) {
                Text("Thông tin liên hệ")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(AppConstants.Colors.textPrimary)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Số điện thoại")
                        .font(.beVietnamPro(.medium, size: 14))
                        .foregroundColor(AppConstants.Colors.textPrimary)
                    
                    TextField("Nhập số điện thoại", text: $phone)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .keyboardType(.phonePad)
                        .focused($focusedField, equals: .phone)
                }
            }
            .padding(16)
            .background(Color(.systemGray6).opacity(0.3))
            .cornerRadius(12)
        }
    }
    
    // MARK: - Save Button Section
    private var saveButtonSection: some View {
        Button(action: {
            Task {
                await saveProfile()
            }
        }) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                
                Text(isLoading ? "Đang lưu..." : "Lưu thay đổi")
                    .font(.beVietnamPro(.semiBold, size: 16))
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                LinearGradient(
                    colors: hasChanges ? [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep] : [Color(.systemGray4), Color(.systemGray5)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .cornerRadius(12)
        }
        .disabled(!hasChanges || isLoading)
    }
    
    // MARK: - Computed Properties
    private var hasChanges: Bool {
        let currentUser = authViewModel.currentUser
        return firstName != (currentUser?.firstName ?? "") ||
               lastName != (currentUser?.lastName ?? "") ||
               phone != (currentUser?.phone ?? "") ||
               !Calendar.current.isDate(dateOfBirth, inSameDayAs: currentUser?.dateOfBirth ?? Date())
    }
    
    // MARK: - Helper Methods
    private func loadCurrentUserData() {
        if let user = authViewModel.currentUser {
            firstName = user.firstName ?? ""
            lastName = user.lastName ?? ""
            phone = user.phone ?? ""
            dateOfBirth = user.dateOfBirth ?? Date()
        }
    }
    
    private func saveProfile() async {
        guard hasChanges else {
            dismiss()
            return
        }
        
        isLoading = true
        
        do {
            let request = UpdateProfileRequest(
                firstName: firstName.isEmpty ? nil : firstName,
                lastName: lastName.isEmpty ? nil : lastName,
                phone: phone.isEmpty ? nil : phone,
                dateOfBirth: dateOfBirth,
                avatar: nil
            )
            
            let authRepository = AuthRepository()
            let _ = try await authRepository.updateProfile(request: request)
            
            // Update the current user in auth state
            await authViewModel.refreshUserData()
            
            showSuccess = true
            
        } catch {
            errorMessage = error.localizedDescription
            showError = true
        }
        
        isLoading = false
    }
}

// MARK: - Date Picker Sheet
struct DatePickerSheet: View {
    @Binding var selectedDate: Date
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                DatePicker(
                    "Chọn ngày sinh",
                    selection: $selectedDate,
                    displayedComponents: .date
                )
                .datePickerStyle(.wheel)
                .padding()
                
                Spacer()
            }
            .navigationTitle("Ngày sinh")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Hủy") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Xong") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

#Preview {
    StudentEditProfileActionSheet()
        .environmentObject(AuthViewModel())
}

//
//  StudentProfileActionSheetDemo.swift
//  VantisEducation
//
//  Created by Augment Agent on 31/7/25.
//

import SwiftUI

struct StudentProfileActionSheetDemo: View {
    @StateObject private var authViewModel = AuthViewModel()
    @State private var showActionSheet = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Profile preview
                VStack(spacing: 16) {
                    // Avatar
                    ZStack {
                        Circle()
                            .fill(Color(.systemGray6))
                            .frame(width: 100, height: 100)
                        
                        ZStack {
                            LinearGradient(
                                gradient: Gradient(colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                            
                            Text("LV")
                                .font(.beVietnamPro(.bold, size: 28))
                                .foregroundColor(.white)
                        }
                        .frame(width: 100, height: 100)
                        .clipShape(Circle())
                    }
                    
                    // User info
                    VStack(spacing: 8) {
                        Text("Lê Văn <PERSON>")
                            .font(.beVietnamPro(.semiBold, size: 20))
                            .foregroundColor(AppConstants.Colors.textPrimary)
                        
                        Text("31 Jul 2025")
                            .font(.beVietnamPro(.regular, size: 16))
                            .foregroundColor(AppConstants.Colors.textSecondary)
                    }
                }
                .padding(.top, 40)
                
                Spacer()
                
                // Demo button
                Button(action: {
                    showActionSheet = true
                }) {
                    HStack {
                        Image(systemName: "pencil")
                            .font(.system(size: 16, weight: .medium))
                        
                        Text("Chỉnh sửa hồ sơ")
                            .font(.beVietnamPro(.semiBold, size: 16))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            colors: [AppConstants.Colors.primary, AppConstants.Colors.primaryDeep],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(12)
                }
                .padding(.horizontal, 20)
                
                Spacer()
            }
            .navigationTitle("Demo Action Sheet")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showActionSheet) {
            StudentEditProfileActionSheet()
                .environmentObject(authViewModel)
                .presentationDetents([.medium, .large])
                .presentationDragIndicator(.visible)
        }
        .onAppear {
            setupMockUser()
        }
    }
    
    private func setupMockUser() {
        authViewModel.currentUser = User(
            id: "demo-user",
            email: "<EMAIL>",
            firstName: "Lê",
            lastName: "Văn Hùng",
            phone: "0123456789",
            role: .student,
            isActive: true,
            avatar: nil,
            dateOfBirth: Calendar.current.date(from: DateComponents(year: 2025, month: 7, day: 31)),
            lastLoginAt: Date(),
            createdAt: Date(),
            updatedAt: Date(),
            businessName: nil,
            businessId: nil,
            category: nil,
            businessPhone: nil,
            website: nil,
            businessDescription: nil,
            businessStatus: nil,
            onboardedAt: nil
        )
    }
}

#Preview {
    StudentProfileActionSheetDemo()
}

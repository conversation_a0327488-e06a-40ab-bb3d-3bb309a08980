//
//  HTMLTextView.swift
//  VantisEducation
//
//  Created by Student App on 31/7/25.
//

import SwiftUI
import UIKit

// MARK: - HTML Text View
struct HTMLTextView: UIViewRepresentable {
    let htmlString: String
    let font: UIFont
    let textColor: UIColor
    
    init(
        htmlString: String,
        font: UIFont = UIFont.systemFont(ofSize: 14),
        textColor: UIColor = UIColor.label
    ) {
        self.htmlString = htmlString
        self.font = font
        self.textColor = textColor
    }
    
    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.isEditable = false
        textView.isScrollEnabled = false
        textView.backgroundColor = UIColor.clear
        textView.textContainerInset = UIEdgeInsets.zero
        textView.textContainer.lineFragmentPadding = 0
        
        // Configure text view
        configureTextView(textView)
        
        return textView
    }
    
    func updateUIView(_ uiView: UITextView, context: Context) {
        configureTextView(uiView)
    }
    
    private func configureTextView(_ textView: UITextView) {
        guard !htmlString.isEmpty else {
            textView.text = ""
            return
        }
        
        // Create attributed string from HTML
        if let attributedString = createAttributedString(from: htmlString) {
            textView.attributedText = attributedString
        } else {
            // Fallback to plain text if HTML parsing fails
            textView.text = htmlString.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
            textView.font = font
            textView.textColor = textColor
        }
    }
    
    private func createAttributedString(from htmlString: String) -> NSAttributedString? {
        // Clean and prepare HTML string
        let cleanedHTML = preprocessHTML(htmlString)
        guard let data = cleanedHTML.data(using: .utf8) else { return nil }

        do {
            let options: [NSAttributedString.DocumentReadingOptionKey: Any] = [
                .documentType: NSAttributedString.DocumentType.html,
                .characterEncoding: String.Encoding.utf8.rawValue
            ]

            let attributedString = try NSMutableAttributedString(
                data: data,
                options: options,
                documentAttributes: nil
            )

            // Apply custom styling
            let range = NSRange(location: 0, length: attributedString.length)

            // Set base font and color
            attributedString.addAttribute(.font, value: font, range: range)
            attributedString.addAttribute(.foregroundColor, value: textColor, range: range)

            // Adjust line spacing for better readability with Vietnamese text
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.lineSpacing = 3
            paragraphStyle.paragraphSpacing = 10
            paragraphStyle.lineBreakMode = .byWordWrapping
            attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: range)

            return attributedString

        } catch {
            print("❌ HTMLTextView: Failed to parse HTML - \(error)")
            return nil
        }
    }

    private func preprocessHTML(_ htmlString: String) -> String {
        var processedHTML = htmlString

        // Add basic HTML structure if missing
        if !processedHTML.lowercased().contains("<html") {
            processedHTML = """
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                        line-height: 1.4;
                        margin: 0;
                        padding: 0;
                    }
                    p { margin: 8px 0; }
                    ul, ol { margin: 8px 0; padding-left: 20px; }
                    li { margin: 4px 0; }
                    b, strong { font-weight: 600; }
                </style>
            </head>
            <body>
                \(processedHTML)
            </body>
            </html>
            """
        }

        return processedHTML
    }
}

// MARK: - SwiftUI Wrapper with Be Vietnam Pro Font
struct HTMLText: View {
    let htmlString: String
    let fontSize: CGFloat
    let fontWeight: UIFont.Weight
    let textColor: Color

    init(
        _ htmlString: String,
        fontSize: CGFloat = 14,
        fontWeight: UIFont.Weight = .regular,
        textColor: Color = .primary
    ) {
        self.htmlString = htmlString
        self.fontSize = fontSize
        self.fontWeight = fontWeight
        self.textColor = textColor
    }

    var body: some View {
        HTMLTextView(
            htmlString: htmlString,
            font: getBeVietnamProFont(size: fontSize, weight: fontWeight),
            textColor: UIColor(textColor)
        )
        .fixedSize(horizontal: false, vertical: true)
    }

    private func getBeVietnamProFont(size: CGFloat, weight: UIFont.Weight) -> UIFont {
        let fontName: String

        switch weight {
        case .bold, .heavy, .black:
            fontName = "BeVietnamPro-Bold"
        case .semibold:
            fontName = "BeVietnamPro-SemiBold"
        case .medium:
            fontName = "BeVietnamPro-Medium"
        default:
            fontName = "BeVietnamPro-Regular"
        }

        return UIFont(name: fontName, size: size) ?? UIFont.systemFont(ofSize: size, weight: weight)
    }
}

// MARK: - Preview
#if DEBUG
struct HTMLTextView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            HTMLText(
                """
                <p><b>Sau khi hoàn thành môn TRUE CEO IDENTITY</b>, người học sẽ đạt được:</p>
                <ul>
                <li><p><b>Nhận diện được phong cách lãnh đạo cá nhân</b>, từ đó xây dựng bản sắc riêng phù hợp với giá trị cốt lõi và mục tiêu phát triển sự nghiệp lãnh đạo.</p></li>
                <li><p><b>Nâng cao tư duy chiến lược và năng lực dẫn dắt tổ chức</b>, giúp học viên ra quyết định hiệu quả trong môi trường kinh doanh đầy biến động.</p></li>
                </ul>
                """,
                fontSize: 14,
                fontWeight: .regular,
                textColor: .secondary
            )
            
            Spacer()
        }
        .padding()
    }
}
#endif

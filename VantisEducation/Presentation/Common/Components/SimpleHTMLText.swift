//
//  SimpleHTMLText.swift
//  VantisEducation
//
//  Created by Student App on 31/7/25.
//

import SwiftUI
import UIKit

// MARK: - Simple HTML Text View
struct SimpleHTMLText: View {
    let htmlString: String
    let fontSize: CGFloat
    let textColor: Color
    
    init(
        _ htmlString: String,
        fontSize: CGFloat = 14,
        textColor: Color = .primary
    ) {
        self.htmlString = htmlString
        self.fontSize = fontSize
        self.textColor = textColor
    }
    
    var body: some View {
        Text(attributedString)
            .multilineTextAlignment(.leading)
    }
    
    private var attributedString: AttributedString {
        // Try to parse HTML first
        if let attributedString = parseHTML() {
            return attributedString
        }
        
        // Fallback to plain text with basic HTML tag removal
        let plainText = htmlString.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
        return AttributedString(plainText)
    }
    
    private func parseHTML() -> AttributedString? {
        guard !htmlString.isEmpty else { return nil }
        
        // Prepare HTML with proper structure
        let fullHTML = """
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { 
                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                    font-size: \(fontSize)px;
                    line-height: 1.4;
                    margin: 0;
                    padding: 0;
                }
                p { margin: 8px 0; }
                ul, ol { margin: 8px 0; padding-left: 20px; }
                li { margin: 4px 0; }
                b, strong { font-weight: 600; }
            </style>
        </head>
        <body>
            \(htmlString)
        </body>
        </html>
        """
        
        guard let data = fullHTML.data(using: .utf8) else { return nil }
        
        do {
            let options: [NSAttributedString.DocumentReadingOptionKey: Any] = [
                .documentType: NSAttributedString.DocumentType.html,
                .characterEncoding: String.Encoding.utf8.rawValue
            ]
            
            let nsAttributedString = try NSAttributedString(
                data: data,
                options: options,
                documentAttributes: nil
            )
            
            // Convert to AttributedString
            return AttributedString(nsAttributedString)
            
        } catch {
            print("❌ SimpleHTMLText: Failed to parse HTML - \(error)")
            return nil
        }
    }
}

// MARK: - Preview
#if DEBUG
struct SimpleHTMLText_Previews: PreviewProvider {
    static var previews: some View {
        VStack(alignment: .leading, spacing: 20) {
            SimpleHTMLText(
                """
                <p><b>Sau khi hoàn thành môn TRUE CEO IDENTITY</b>, người học sẽ đạt được:</p>
                <ul>
                <li><p><b>Nhận diện được phong cách lãnh đạo cá nhân</b>, từ đó xây dựng bản sắc riêng phù hợp với giá trị cốt lõi và mục tiêu phát triển sự nghiệp lãnh đạo.</p></li>
                <li><p><b>Nâng cao tư duy chiến lược và năng lực dẫn dắt tổ chức</b>, giúp học viên ra quyết định hiệu quả trong môi trường kinh doanh đầy biến động.</p></li>
                </ul>
                """,
                fontSize: 14,
                textColor: .secondary
            )
            
            Spacer()
        }
        .padding()
    }
}
#endif

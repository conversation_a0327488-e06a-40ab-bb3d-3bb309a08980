//
//  SimpleHTMLText.swift
//  VantisEducation
//
//  Created by Student App on 31/7/25.
//

import SwiftUI
import UIKit

// MARK: - Simple HTML Text View
struct SimpleHTMLText: View {
    let htmlString: String
    let fontSize: CGFloat
    let textColor: Color
    let maxLines: Int?
    let showExpandButton: Bool

    @State private var isExpanded = false
    @State private var isTruncated = false

    init(
        _ htmlString: String,
        fontSize: CGFloat = 14,
        textColor: Color = .primary,
        maxLines: Int? = nil,
        showExpandButton: Bool = false
    ) {
        self.htmlString = htmlString
        self.fontSize = fontSize
        self.textColor = textColor
        self.maxLines = maxLines
        self.showExpandButton = showExpandButton
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(attributedString)
                .multilineTextAlignment(.leading)
                .lineLimit(shouldShowLineLimit ? maxLines : nil)
                .onAppear {
                    // Simple check for truncation based on content length
                    if showExpandButton, let maxLines = maxLines {
                        let plainText = htmlString.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
                        // More conservative estimate for mobile screens
                        let estimatedCharsPerLine = 25 // Further reduced for mobile
                        let estimatedMaxChars = maxLines * estimatedCharsPerLine
                        isTruncated = plainText.count > estimatedMaxChars

                        // Debug print
                        print("🔍 SimpleHTMLText Debug:")
                        print("   Plain text length: \(plainText.count)")
                        print("   Max chars for \(maxLines) lines: \(estimatedMaxChars)")
                        print("   Is truncated: \(isTruncated)")
                        print("   Plain text preview: \(String(plainText.prefix(100)))...")
                    }
                }

            if showExpandButton && isTruncated {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        isExpanded.toggle()
                    }
                }) {
                    Text(isExpanded ? "Thu gọn" : "Xem thêm")
                        .font(.beVietnamPro(.medium, size: fontSize - 1))
                        .foregroundColor(AppConstants.Colors.primary)
                }
            }
        }
    }

    private var shouldShowLineLimit: Bool {
        return showExpandButton && !isExpanded && maxLines != nil
    }

    private var attributedString: AttributedString {
        // Try to parse HTML first
        if let attributedString = parseHTML() {
            return attributedString
        }

        // Fallback to plain text with basic HTML tag removal
        let plainText = htmlString.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
        return AttributedString(plainText)
    }

    private func parseHTML() -> AttributedString? {
        guard !htmlString.isEmpty else { return nil }

        // Prepare HTML with proper structure
        let fullHTML = """
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                    font-size: \(fontSize)px;
                    line-height: 1.4;
                    margin: 0;
                    padding: 0;
                }
                p { margin: 8px 0; }
                ul, ol { margin: 8px 0; padding-left: 20px; }
                li { margin: 4px 0; }
                b, strong { font-weight: 600; }
            </style>
        </head>
        <body>
            \(htmlString)
        </body>
        </html>
        """

        guard let data = fullHTML.data(using: .utf8) else { return nil }

        do {
            let options: [NSAttributedString.DocumentReadingOptionKey: Any] = [
                .documentType: NSAttributedString.DocumentType.html,
                .characterEncoding: String.Encoding.utf8.rawValue
            ]

            let nsAttributedString = try NSAttributedString(
                data: data,
                options: options,
                documentAttributes: nil
            )

            // Convert to AttributedString
            return AttributedString(nsAttributedString)

        } catch {
            print("❌ SimpleHTMLText: Failed to parse HTML - \(error)")
            return nil
        }
    }
}



// MARK: - Preview
#if DEBUG
struct SimpleHTMLText_Previews: PreviewProvider {
    static var previews: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Normal HTML Text")
                    .font(.headline)

                SimpleHTMLText(
                    """
                    <p><b>Sau khi hoàn thành môn TRUE CEO IDENTITY</b>, người học sẽ đạt được:</p>
                    <ul>
                    <li><p><b>Nhận diện được phong cách lãnh đạo cá nhân</b>, từ đó xây dựng bản sắc riêng phù hợp với giá trị cốt lõi và mục tiêu phát triển sự nghiệp lãnh đạo.</p></li>
                    <li><p><b>Nâng cao tư duy chiến lược và năng lực dẫn dắt tổ chức</b>, giúp học viên ra quyết định hiệu quả trong môi trường kinh doanh đầy biến động.</p></li>
                    </ul>
                    """,
                    fontSize: 14,
                    textColor: .secondary
                )

                Divider()

                Text("HTML Text with 3-line limit + Expand")
                    .font(.headline)

                SimpleHTMLText(
                    """
                    <p>Hoàn thành môn Trợ lý giám đốc, người học đạt được các năng lực sau:</p>
                    <p><b>Thành thạo kỹ năng giao tiếp chuyên nghiệp</b>, ứng xử linh hoạt trong mọi tình huống doanh nghiệp, tạo dựng hình ảnh trợ lý tin cậy và hiệu quả.</p>
                    <p><b>Quản lý thời gian, lịch trình và công việc của lãnh đạo một cách hệ thống</b>, đảm bảo tính hiệu quả tối ưu hóa năng suất làm việc.</p>
                    <p><b>Xử lý thông tin và tình huống nhạy bén</b>, hỗ trợ giám đốc trong việc tổng hợp, phân tích và ra quyết định chiến lược.</p>
                    <p><b>Thành thạo kỹ năng tổ chức cuộc họp, hội nghị và sự kiện</b>, đóng thói biết cách phối hợp với các bộ phận liên quan để đạt mục tiêu chung.</p>
                    """,
                    fontSize: 14,
                    textColor: .secondary,
                    maxLines: 3,
                    showExpandButton: true
                )

                Spacer()
            }
            .padding()
        }
    }
}
#endif

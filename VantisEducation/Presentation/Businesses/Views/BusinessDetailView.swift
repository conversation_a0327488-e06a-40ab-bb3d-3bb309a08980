import SwiftUI
import MapKit

struct BusinessDetailView: View {
    let merchant: Business
    @Environment(\.dismiss) private var dismiss
    @State private var showingMap = false
    @State private var showingContact = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header section
                    headerSection
                    
                    // Quick actions
                    quickActionsSection
                    
                    // Information sections
                    infoSection
                    
                    // Operating hours
                    operatingHoursSection
                    
                    // Statistics
                    statisticsSection
                    
                    // Location section
                    locationSection
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 20)
            }
            .navigationTitle("Business Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingContact = true
                    }) {
                        Image(systemName: "phone")
                    }
                }
            }
        }
        .sheet(isPresented: $showingMap) {
            BusinessMapView(merchant: merchant)
        }
        .sheet(isPresented: $showingContact) {
            BusinessContactView(merchant: merchant)
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // Logo and basic info
            HStack(spacing: 16) {
                AsyncImage(url: URL(string: merchant.logoUrl ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray5))
                        .overlay(
                            Image(systemName: "building.2")
                                .font(.title)
                                .foregroundColor(.secondary)
                        )
                }
                .frame(width: 80, height: 80)
                .cornerRadius(12)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text(merchant.name)
                        .font(.title2)
                        .fontWeight(.bold)
                        .multilineTextAlignment(.leading)
                    
                    HStack {
                        Text(merchant.category.displayName)
                            .font(.subheadline)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(AppConstants.Colors.primary)
                            )
                        
                        Spacer()
                        
                        // Status indicator
                        HStack(spacing: 4) {
                            Circle()
                                .fill(merchant.isActive ? Color.green : Color.orange)
                                .frame(width: 8, height: 8)
                            
                            Text(merchant.status.displayName)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    if let description = merchant.description {
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(3)
                            .multilineTextAlignment(.leading)
                    }
                }
                
                Spacer()
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Quick Actions Section
    private var quickActionsSection: some View {
        HStack(spacing: 12) {
            BusinessQuickActionButton(
                icon: "phone.fill",
                title: "Call",
                color: .green
            ) {
                showingContact = true
            }

            BusinessQuickActionButton(
                icon: "location.fill",
                title: "Location",
                color: .blue
            ) {
                showingMap = true
            }
            
            if let website = merchant.website, let url = URL(string: website) {
                BusinessQuickActionButton(
                    icon: "globe",
                    title: "Website",
                    color: .orange
                ) {
                    UIApplication.shared.open(url)
                }
            }
            
            BusinessQuickActionButton(
                icon: "qrcode",
                title: "QR Code",
                color: AppConstants.Colors.primary
            ) {
                // TODO: Show QR code for payment
            }
        }
    }
    
    // MARK: - Information Section
    private var infoSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "Information")
            
            VStack(spacing: 12) {
                BusinessInfoRow(
                    icon: "envelope",
                    title: "Email",
                    value: merchant.email
                )

                if let phone = merchant.phone {
                    BusinessInfoRow(
                        icon: "phone",
                        title: "Phone",
                        value: phone
                    )
                }

                BusinessInfoRow(
                    icon: "building",
                    title: "Business ID",
                    value: merchant.businessId
                )

                BusinessInfoRow(
                    icon: "percent",
                    title: "Commission Rate",
                    value: merchant.displayBusinessInfo
                )

                if let address = merchant.address {
                    BusinessInfoRow(
                        icon: "location",
                        title: "Address",
                        value: merchant.displayAddress
                    )
                }
            }
        }
    }
    
    // MARK: - Operating Hours Section
    private var operatingHoursSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "Operating Hours")
            
            if let operatingHours = merchant.operatingHours, !operatingHours.isEmpty {
                VStack(spacing: 8) {
                    ForEach(operatingHours.sorted(by: { $0.dayOfWeek < $1.dayOfWeek }), id: \.dayOfWeek) { hour in
                        HStack {
                            Text(dayName(for: hour.dayOfWeek))
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .frame(width: 80, alignment: .leading)
                            
                            Spacer()
                            
                            if hour.openTime == "Closed" {
                                Text("Closed")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            } else {
                                Text("\(hour.openTime) - \(hour.closeTime)")
                                    .font(.subheadline)
                                    .foregroundColor(.primary)
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(.systemGray6))
                        )
                    }
                }
            } else {
                Text("Operating hours not available")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 20)
            }
        }
    }
    
    // MARK: - Statistics Section
    private var statisticsSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "Statistics")
            
            HStack(spacing: 16) {
                BusinessStatCard(
                    title: "Transactions",
                    value: "\(merchant.totalTransactions)",
                    icon: "creditcard",
                    color: .blue
                )

                BusinessStatCard(
                    title: "Commission Paid",
                    value: "\(merchant.totalTransactions)",
                    icon: "banknote",
                    color: .green
                )
            }

            HStack(spacing: 16) {
                BusinessStatCard(
                    title: "Settlement Received",
                    value: "N/A",
                    icon: "arrow.down.circle",
                    color: .orange
                )

                BusinessStatCard(
                    title: "Last Transaction",
                    value: formatDate(merchant.lastTransactionAt),
                    icon: "clock",
                    color: .purple
                )
            }
        }
    }
    
    // MARK: - Location Section
    private var locationSection: some View {
        VStack(spacing: 16) {
            SectionHeader(title: "Location")
            
            if let address = merchant.address,
               let latitude = address.latitude,
               let longitude = address.longitude {
                
                Button(action: {
                    showingMap = true
                }) {
                    VStack(spacing: 12) {
                        Map(coordinateRegion: .constant(
                            MKCoordinateRegion(
                                center: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
                                span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                            )
                        ), annotationItems: [merchant]) { merchant in
                            MapPin(coordinate: CLLocationCoordinate2D(
                                latitude: latitude,
                                longitude: longitude
                            ), tint: .red)
                        }
                        .frame(height: 200)
                        .cornerRadius(12)
                        .disabled(true)
                        
                        Text(merchant.displayAddress)
                            .font(.subheadline)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                    }
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                Text("Location not available")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 20)
            }
        }
    }
    
    // MARK: - Helper Functions
    private func dayName(for dayOfWeek: Int) -> String {
        let days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
        return days[dayOfWeek]
    }
    
    private func formatCurrency(_ amount: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "VND"
        formatter.maximumFractionDigits = 0
        return formatter.string(from: NSNumber(value: amount)) ?? "₫\(Int(amount))"
    }
    
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "N/A" }
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Supporting Views
// QuickActionButton is defined in AssignmentListComponents.swift
struct BusinessQuickActionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(color)
                    )

                Text(title)
                    .font(.caption)
                    .foregroundColor(.primary)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct SectionHeader: View {
    let title: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            Spacer()
        }
    }
}

struct BusinessInfoRow: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(AppConstants.Colors.primary)
                .frame(width: 20)
            
            Text(title)
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textSecondary)
                .frame(width: 100, alignment: .leading)

            Text(value)
                .font(.subheadline)
                .foregroundColor(AppConstants.Colors.textPrimary)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(AppConstants.Colors.surface)
        )
    }
}

struct BusinessStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
                .lineLimit(1)
                .minimumScaleFactor(0.8)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Supporting Sheet Views
struct BusinessMapView: View {
    let merchant: Business
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            Group {
                if let address = merchant.address,
                   let latitude = address.latitude,
                   let longitude = address.longitude {

                    Map(coordinateRegion: .constant(
                        MKCoordinateRegion(
                            center: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
                            span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
                        )
                    ), annotationItems: [merchant]) { merchant in
                        MapAnnotation(coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude)) {
                            VStack {
                                Image(systemName: "building.2")
                                    .foregroundColor(.white)
                                    .padding(8)
                                    .background(Circle().fill(Color.red))

                                Text(merchant.name)
                                    .font(.caption)
                                    .padding(4)
                                    .background(Color.white)
                                    .cornerRadius(4)
                            }
                        }
                    }
                } else {
                    VStack {
                        Image(systemName: "location.slash")
                            .font(.largeTitle)
                            .foregroundColor(.secondary)

                        Text("Location not available")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle(merchant.name)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct BusinessContactView: View {
    let merchant: Business
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 12) {
                    AsyncImage(url: URL(string: merchant.logoUrl ?? "")) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray5))
                            .overlay(
                                Image(systemName: "building.2")
                                    .font(.title)
                                    .foregroundColor(.secondary)
                            )
                    }
                    .frame(width: 80, height: 80)
                    .cornerRadius(12)

                    Text(merchant.name)
                        .font(.title2)
                        .fontWeight(.bold)
                }
                .padding(.top, 20)

                // Contact options
                VStack(spacing: 16) {
                    if let phone = merchant.phone {
                        ContactButton(
                            icon: "phone.fill",
                            title: "Call",
                            subtitle: phone,
                            color: .green
                        ) {
                            if let url = URL(string: "tel:\(phone)") {
                                UIApplication.shared.open(url)
                            }
                        }
                    }

                    ContactButton(
                        icon: "envelope.fill",
                        title: "Email",
                        subtitle: merchant.email,
                        color: .blue
                    ) {
                        if let url = URL(string: "mailto:\(merchant.email)") {
                            UIApplication.shared.open(url)
                        }
                    }

                    if let website = merchant.website {
                        ContactButton(
                            icon: "globe",
                            title: "Website",
                            subtitle: website,
                            color: .orange
                        ) {
                            if let url = URL(string: website) {
                                UIApplication.shared.open(url)
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)

                Spacer()
            }
            .navigationTitle("Contact")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ContactButton: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .foregroundColor(.white)
                    .frame(width: 44, height: 44)
                    .background(Circle().fill(color))

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct BusinessDetailView_Previews: PreviewProvider {
    static var previews: some View {
        BusinessDetailView(merchant: BusinessesViewModel().businesses.first!)
    }
}

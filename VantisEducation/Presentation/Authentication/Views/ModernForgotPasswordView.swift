//
//  ModernForgotPasswordView.swift
//  VantisEducation
//
//  Created by Augment Agent on 31/7/25.
//

import SwiftUI

struct ModernForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var email = ""
    @State private var isLoading = false
    @State private var showSuccess = false
    @State private var showError = false
    @State private var errorMessage = ""
    @State private var emailError: String?
    @State private var animateContent = false
    
    var body: some View {
        NavigationView {
            GeometryReader { geometry in
                ZStack {
                    // Background
                    backgroundView
                    
                    ScrollView {
                        VStack(spacing: 32) {
                            // Header Section
                            headerSection
                                .frame(minHeight: geometry.size.height * 0.3)
                                .opacity(animateContent ? 1 : 0)
                                .offset(y: animateContent ? 0 : -30)
                            
                            // Form Section
                            formSection
                                .opacity(animateContent ? 1 : 0)
                                .offset(y: animateContent ? 0 : 50)
                            
                            Spacer(minLength: 40)
                        }
                    }
                    .scrollIndicators(.hidden)
                    .scrollDismissesKeyboard(.interactively)
                }
            }
            .navigationBarHidden(true)
            .onTapGesture {
                hideKeyboard()
            }
            .onAppear {
                withAnimation(.easeOut(duration: 0.8).delay(0.2)) {
                    animateContent = true
                }
            }
            .alert("Thành công", isPresented: $showSuccess) {
                Button("Đồng ý") {
                    dismiss()
                }
            } message: {
                Text("Hướng dẫn đặt lại mật khẩu đã được gửi đến email của bạn.")
            }
            .alert("Lỗi", isPresented: $showError) {
                Button("Đồng ý") {}
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    // MARK: - Background View
    private var backgroundView: some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color(red: 0.95, green: 0.97, blue: 1.0),
                    Color(red: 0.98, green: 0.95, blue: 1.0),
                    Color.white
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            // Subtle pattern overlay
            Circle()
                .fill(
                    RadialGradient(
                        colors: [
                            Color.blue.opacity(0.08),
                            Color.clear
                        ],
                        center: .topTrailing,
                        startRadius: 0,
                        endRadius: 200
                    )
                )
                .frame(width: 300, height: 300)
                .offset(x: 100, y: -150)
        }
        .ignoresSafeArea()
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 24) {
            // Back Button
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.primary)
                }
                
                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.top, 20)
            
            Spacer()
            
            // Icon and Title
            VStack(spacing: 20) {
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color.orange, Color.red],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 70, height: 70)
                        .shadow(color: Color.orange.opacity(0.3), radius: 15, x: 0, y: 8)
                    
                    Image(systemName: "key.fill")
                        .font(.system(size: 28, weight: .medium))
                        .foregroundColor(.white)
                }
                
                VStack(spacing: 12) {
                    Text("Quên mật khẩu?")
                        .font(.system(size: 26, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Text("Đừng lo lắng! Nhập địa chỉ email của bạn và chúng tôi sẽ gửi hướng dẫn để đặt lại mật khẩu.")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(3)
                        .padding(.horizontal, 20)
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Form Section
    private var formSection: some View {
        VStack(spacing: 24) {
            VStack(spacing: 20) {
                ModernTextField(
                    title: "Địa chỉ Email",
                    placeholder: "Nhập email của bạn",
                    text: $email,
                    keyboardType: .emailAddress,
                    textContentType: .emailAddress,
                    icon: "envelope",
                    errorMessage: emailError
                )
                .onChange(of: email) { _, newValue in
                    validateEmail(newValue)
                }
            }
            
            // Reset Button
            Button(action: {
                hideKeyboard()
                Task {
                    await resetPassword()
                }
            }) {
                HStack(spacing: 12) {
                    if isLoading {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "paperplane.fill")
                            .font(.system(size: 16, weight: .medium))
                    }
                    
                    Text(isLoading ? "Đang gửi..." : "Gửi hướng dẫn đặt lại")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: isFormValid ? [Color.orange, Color.red] : [Color(.systemGray4), Color(.systemGray5)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(
                            color: isFormValid ? Color.orange.opacity(0.3) : Color.clear,
                            radius: 10,
                            x: 0,
                            y: 5
                        )
                )
            }
            .disabled(!isFormValid || isLoading)
            .scaleEffect(isLoading ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isLoading)
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 32)
        .background(
            RoundedRectangle(cornerRadius: 32)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.05), radius: 20, x: 0, y: -5)
        )
        .padding(.horizontal, 20)
    }
    
    // MARK: - Computed Properties
    private var isFormValid: Bool {
        return !email.isEmpty && emailError == nil && email.isValidEmail
    }
    
    // MARK: - Helper Methods
    private func validateEmail(_ email: String) {
        if email.isEmpty {
            emailError = nil
        } else if !email.isValidEmail {
            emailError = "Vui lòng nhập địa chỉ email hợp lệ"
        } else {
            emailError = nil
        }
    }
    
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    private func resetPassword() async {
        guard isFormValid else { return }

        isLoading = true

        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        do {
            // Make actual API call
            let response = try await APIClient.shared.request(
                endpoint: APIEndpoints.Auth.forgotPassword,
                method: .POST,
                parameters: ["email": email],
                responseType: ForgotPasswordResponse.self,
                requiresAuth: false
            )

            if response.resetTokenSent {
                showSuccess = true
            } else {
                errorMessage = response.message
                showError = true
            }

        } catch {
            errorMessage = "Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại."
            showError = true
        }

        isLoading = false
    }
}

// MARK: - Preview
struct ModernForgotPasswordView_Previews: PreviewProvider {
    static var previews: some View {
        ModernForgotPasswordView()
    }
}

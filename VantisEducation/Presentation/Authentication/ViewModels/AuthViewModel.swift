//
//  AuthViewModel.swift
//  linkx-mobile-ios
//
//  Created by LinkX Team on 20/7/25.
//

import Foundation
import SwiftUI
import Combine

// Simple AuthViewModel for student login
@MainActor
class AuthViewModel: ObservableObject {
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var authError: String?
    @Published var showError = false
    @Published var loginUsername = ""
    @Published var loginPassword = ""
    @Published var loginEmail = ""  // For compatibility
    @Published var rememberMe = false
    @Published var biometricEnabled = false

    private let studentAuthService = StudentAuthService.shared
    private var cancellables = Set<AnyCancellable>()

    init() {
        // Observe StudentAuthService changes
        studentAuthService.$isAuthenticated
            .receive(on: DispatchQueue.main)
            .sink { [weak self] value in
                self?.isAuthenticated = value
            }
            .store(in: &cancellables)

        studentAuthService.$currentUser
            .receive(on: DispatchQueue.main)
            .sink { [weak self] value in
                self?.currentUser = value
            }
            .store(in: &cancellables)

        studentAuthService.$isLoading
            .receive(on: DispatchQueue.main)
            .sink { [weak self] value in
                self?.isLoading = value
            }
            .store(in: &cancellables)

        studentAuthService.$authError
            .receive(on: DispatchQueue.main)
            .sink { [weak self] value in
                self?.authError = value
                self?.showError = value != nil
            }
            .store(in: &cancellables)
    }

    func login() async {
        do {
            try await studentAuthService.login(
                username: loginUsername.isEmpty ? loginEmail : loginUsername,
                password: loginPassword,
                rememberMe: rememberMe
            )
        } catch {
            print("Login failed: \(error)")
        }
    }

    func logout() async {
        await studentAuthService.logout()
    }

    func checkAuthState() {
        studentAuthService.checkAuthState()
    }

    func refreshUserData() async {
        // Simple refresh - just check auth state
        checkAuthState()
    }

    // Computed properties for UI
    var isStudent: Bool {
        return currentUser?.role == .student
    }

    var userDisplayName: String {
        guard let user = currentUser else { return "Không xác định" }
        return "\(user.firstName ?? "") \(user.lastName ?? "")".trimmingCharacters(in: .whitespaces)
    }

    var permissionsCount: Int {
        return 0  // Simplified for now
    }

    var biometricType: String {
        return "TouchID"  // Simplified for now
    }

    var userRoleDisplayName: String {
        return isStudent ? "Học sinh" : "Không xác định"
    }

    var isLoginFormValid: Bool {
        let email = loginUsername.isEmpty ? loginEmail : loginUsername
        return !email.isEmpty && !loginPassword.isEmpty
    }
    
    // UI compatibility properties
    var errorMessage: String? {
        return authError
    }

    func hideError() {
        authError = nil
        showError = false
    }

    func showErrorMessage(_ message: String) {
        authError = message
        showError = true
    }

    func validateEmail(_ email: String) -> String? {
        if email.isEmpty {
            return "Email là bắt buộc"
        }
        if !email.contains("@") {
            return "Định dạng email không hợp lệ"
        }
        return nil
    }

    func validatePassword(_ password: String) -> String? {
        if password.isEmpty {
            return "Password is required"
        }
        if password.count < 6 {
            return "Password must be at least 6 characters"
        }
        return nil
    }

    func loginWithBiometric() async {
        // Placeholder for biometric login
        print("Biometric login not implemented yet")
    }

    func hasPermission(_ permission: String) -> Bool {
        // Simplified permission check
        return isAuthenticated && isStudent
    }

    func canPerformAction(_ action: String) -> Bool {
        // Simplified action check
        return isAuthenticated && isStudent
    }

    func getAuthStatus() -> [String: Any] {
        return [
            "isAuthenticated": isAuthenticated,
            "isStudent": isStudent,
            "userDisplayName": userDisplayName,
            "permissionsCount": permissionsCount
        ]
    }

    func enableBiometricLogin() async {
        // Placeholder for biometric setup
        print("Enable biometric login not implemented yet")
    }

    func disableBiometricLogin() async {
        // Placeholder for biometric disable
        print("Disable biometric login not implemented yet")
    }

    func getValidationReport() -> [String: Any] {
        return [
            "isValid": isAuthenticated,
            "errors": [],
            "warnings": []
        ]
    }
}
